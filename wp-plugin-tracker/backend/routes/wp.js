const express = require('express');
const router = express.Router();

// Mock download stats data
const mockDownloadStats = {
  'embedpress': {
    yesterday: 1234,
    dayBeforeYesterday: 1178,
    change: 56,
    changePercentage: 4.8
  },
  'schedulepress': {
    yesterday: 892,
    dayBeforeYesterday: 945,
    change: -53,
    changePercentage: -5.6
  },
  'notificationx': {
    yesterday: 1567,
    dayBeforeYesterday: 1489,
    change: 78,
    changePercentage: 5.2
  }
};

// GET /api/wp/plugin/:slug/downloads
router.get('/plugin/:slug/downloads', async (req, res) => {
  try {
    const { slug } = req.params;
    const stats = mockDownloadStats[slug];
    
    if (!stats) {
      return res.status(404).json({
        success: false,
        message: 'Download stats not found for this plugin'
      });
    }

    res.json({
      success: true,
      data: {
        data: stats
      }
    });
  } catch (error) {
    console.error('Error fetching download stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch download stats'
    });
  }
});

// GET /api/wp/plugin/:slug
router.get('/plugin/:slug', async (req, res) => {
  try {
    const { slug } = req.params;
    
    // Mock plugin info
    const pluginInfo = {
      name: slug.charAt(0).toUpperCase() + slug.slice(1),
      slug: slug,
      version: '1.0.0',
      author: 'WPDeveloper',
      requires: '5.0',
      tested: '6.4',
      downloaded: 1000000,
      rating: 4.5,
      num_ratings: 150,
      active_installs: 50000,
      last_updated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: pluginInfo
    });
  } catch (error) {
    console.error('Error fetching plugin info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch plugin info'
    });
  }
});

// POST /api/wp/store-download-data
router.post('/store-download-data', async (req, res) => {
  try {
    // Mock storing download data
    res.json({
      success: true,
      message: 'Download data stored successfully'
    });
  } catch (error) {
    console.error('Error storing download data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to store download data'
    });
  }
});

// GET /api/wp/download-data
router.get('/download-data', async (req, res) => {
  try {
    res.json({
      success: true,
      data: mockDownloadStats
    });
  } catch (error) {
    console.error('Error fetching download data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch download data'
    });
  }
});

module.exports = router;
