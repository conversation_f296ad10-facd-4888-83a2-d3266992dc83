const express = require("express");
const router = express.Router();

// Mock download stats data
const mockDownloadStats = {
  embedpress: {
    yesterday: 1234,
    dayBeforeYesterday: 1178,
    change: 56,
    changePercentage: 4.8,
  },
  schedulepress: {
    yesterday: 892,
    dayBeforeYesterday: 945,
    change: -53,
    changePercentage: -5.6,
  },
  notificationx: {
    yesterday: 1567,
    dayBeforeYesterday: 1489,
    change: 78,
    changePercentage: 5.2,
  },
};

// GET /api/wp/plugin/:slug/downloads
router.get("/plugin/:slug/downloads", async (req, res) => {
  try {
    const { slug } = req.params;
    const stats = mockDownloadStats[slug];

    if (!stats) {
      return res.status(404).json({
        success: false,
        message: "Download stats not found for this plugin",
      });
    }

    res.json({
      success: true,
      data: {
        data: stats,
      },
    });
  } catch (error) {
    console.error("Error fetching download stats:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch download stats",
    });
  }
});

// GET /api/wp/plugin/:slug
router.get("/plugin/:slug", async (req, res) => {
  try {
    const { slug } = req.params;

    // Mock plugin info
    const pluginInfo = {
      name: slug.charAt(0).toUpperCase() + slug.slice(1),
      slug: slug,
      version: "1.0.0",
      author: "WPDeveloper",
      requires: "5.0",
      tested: "6.4",
      downloaded: 1000000,
      rating: 4.5,
      num_ratings: 150,
      active_installs: 50000,
      last_updated: new Date().toISOString(),
    };

    res.json({
      success: true,
      data: pluginInfo,
    });
  } catch (error) {
    console.error("Error fetching plugin info:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch plugin info",
    });
  }
});

// POST /api/wp/store-download-data
router.post("/store-download-data", async (req, res) => {
  try {
    // Mock storing download data
    res.json({
      success: true,
      message: "Download data stored successfully",
    });
  } catch (error) {
    console.error("Error storing download data:", error);
    res.status(500).json({
      success: false,
      message: "Failed to store download data",
    });
  }
});

// GET /api/wp/download-data
router.get("/download-data", async (req, res) => {
  try {
    res.json({
      success: true,
      data: mockDownloadStats,
    });
  } catch (error) {
    console.error("Error fetching download data:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch download data",
    });
  }
});

// GET /api/wp/plugin-rank/:slug/:keyword
router.get("/plugin-rank/:slug/:keyword", async (req, res) => {
  try {
    const { slug, keyword } = req.params;

    // Mock plugin ranking data for keyword analytics
    const mockRankings = {
      embedpress: {
        "embed youtube wordpress": { currentRank: 3, previousRank: 5 },
        "wordpress embed": { currentRank: 2, previousRank: 2 },
        "video embed": { currentRank: 7, previousRank: 8 },
        "youtube embed": { currentRank: 1, previousRank: 1 },
      },
      schedulepress: {
        "wordpress scheduling": { currentRank: 4, previousRank: 6 },
        "post scheduler": { currentRank: 2, previousRank: 3 },
        "content scheduling": { currentRank: 5, previousRank: 4 },
      },
      notificationx: {
        "wordpress notifications": { currentRank: 2, previousRank: 3 },
        "social proof": { currentRank: 1, previousRank: 1 },
        "notification popup": { currentRank: 6, previousRank: 7 },
      },
    };

    const pluginRankings = mockRankings[slug];
    if (!pluginRankings) {
      return res.status(404).json({
        success: false,
        message: "Plugin rankings not found",
      });
    }

    const keywordRanking = pluginRankings[keyword.toLowerCase()];
    if (!keywordRanking) {
      // Return default ranking if keyword not found
      return res.json({
        success: true,
        data: {
          currentRank: null,
          previousRank: null,
          rankChange: 0,
          keyword: keyword,
          plugin: slug,
        },
      });
    }

    const rankChange = keywordRanking.previousRank
      ? keywordRanking.previousRank - keywordRanking.currentRank
      : 0;

    res.json({
      success: true,
      data: {
        currentRank: keywordRanking.currentRank,
        previousRank: keywordRanking.previousRank,
        rankChange: rankChange,
        keyword: keyword,
        plugin: slug,
      },
    });
  } catch (error) {
    console.error("Error fetching plugin ranking:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch plugin ranking",
    });
  }
});

// POST /api/wp/batch-plugin-ranks
router.post("/batch-plugin-ranks", async (req, res) => {
  try {
    const { keywords } = req.body;

    if (!keywords || !Array.isArray(keywords)) {
      return res.status(400).json({
        success: false,
        message: "Keywords array is required",
      });
    }

    // Mock batch ranking results
    const results = keywords.map(({ keyword, pluginSlug }) => {
      // Use the same mock data as the individual endpoint
      const mockRankings = {
        embedpress: {
          "embed youtube wordpress": { currentRank: 3, previousRank: 5 },
          "wordpress embed": { currentRank: 2, previousRank: 2 },
          "video embed": { currentRank: 7, previousRank: 8 },
          "youtube embed": { currentRank: 1, previousRank: 1 },
        },
        schedulepress: {
          "wordpress scheduling": { currentRank: 4, previousRank: 6 },
          "post scheduler": { currentRank: 2, previousRank: 3 },
          "content scheduling": { currentRank: 5, previousRank: 4 },
        },
        notificationx: {
          "wordpress notifications": { currentRank: 2, previousRank: 3 },
          "social proof": { currentRank: 1, previousRank: 1 },
          "notification popup": { currentRank: 6, previousRank: 7 },
        },
      };

      const pluginRankings = mockRankings[pluginSlug];
      const keywordRanking = pluginRankings?.[keyword.toLowerCase()];

      if (!keywordRanking) {
        return {
          keyword,
          pluginSlug,
          currentRank: null,
          previousRank: null,
          rankChange: 0,
        };
      }

      const rankChange = keywordRanking.previousRank
        ? keywordRanking.previousRank - keywordRanking.currentRank
        : 0;

      return {
        keyword,
        pluginSlug,
        currentRank: keywordRanking.currentRank,
        previousRank: keywordRanking.previousRank,
        rankChange: rankChange,
      };
    });

    res.json({
      success: true,
      data: results,
    });
  } catch (error) {
    console.error("Error fetching batch plugin rankings:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch batch plugin rankings",
    });
  }
});

module.exports = router;
