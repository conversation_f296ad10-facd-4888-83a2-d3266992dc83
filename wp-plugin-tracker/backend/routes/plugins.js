const express = require('express');
const router = express.Router();

// Mock data for development
const mockPlugins = [
  {
    _id: '1',
    name: 'EmbedP<PERSON>',
    slug: 'embedpress',
    version: '3.9.15',
    currentRank: 45,
    lastReleaseDate: '2024-06-10',
    downloads: 1500000,
    rating: 4.8,
    activeInstalls: 100000
  },
  {
    _id: '2', 
    name: 'SchedulePress',
    slug: 'schedulepress',
    version: '2.1.8',
    currentRank: 78,
    lastReleaseDate: '2024-06-08',
    downloads: 850000,
    rating: 4.6,
    activeInstalls: 75000
  },
  {
    _id: '3',
    name: 'NotificationX',
    slug: 'notificationx',
    version: '2.8.4',
    currentRank: 32,
    lastReleaseDate: '2024-06-12',
    downloads: 2100000,
    rating: 4.9,
    activeInstalls: 150000
  }
];

const mockDownloadStats = {
  'embedpress': {
    yesterday: 1234,
    dayBeforeYesterday: 1178,
    change: 56,
    changePercentage: 4.8
  },
  'schedulepress': {
    yesterday: 892,
    dayBeforeYesterday: 945,
    change: -53,
    changePercentage: -5.6
  },
  'notificationx': {
    yesterday: 1567,
    dayBeforeYesterday: 1489,
    change: 78,
    changePercentage: 5.2
  }
};

// GET /api/plugins/dashboard
router.get('/dashboard', async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        plugins: mockPlugins
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard data'
    });
  }
});

// GET /api/plugins/stats
router.get('/stats', async (req, res) => {
  try {
    const stats = {
      totalPlugins: mockPlugins.length,
      totalDownloads: mockPlugins.reduce((sum, plugin) => sum + plugin.downloads, 0),
      averageRating: mockPlugins.reduce((sum, plugin) => sum + plugin.rating, 0) / mockPlugins.length,
      totalActiveInstalls: mockPlugins.reduce((sum, plugin) => sum + plugin.activeInstalls, 0)
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching plugin stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch plugin stats'
    });
  }
});

// GET /api/plugins
router.get('/', async (req, res) => {
  try {
    res.json({
      success: true,
      data: mockPlugins
    });
  } catch (error) {
    console.error('Error fetching plugins:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch plugins'
    });
  }
});

module.exports = router;
