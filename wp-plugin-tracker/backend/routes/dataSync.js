const express = require("express");
const { protect } = require("../middleware/auth");
const dataSyncService = require("../services/dataSyncService");
const Plugin = require("../models/Plugin");
const DownloadHistory = require("../models/DownloadHistory");

const router = express.Router();

// All routes are protected
router.use(protect);

// @desc    Get data sync status
// @route   GET /api/data-sync/status
// @access  Private
router.get("/status", async (req, res) => {
  try {
    const status = dataSyncService.getSyncStatus();
    const summary = await dataSyncService.getDataSummary();

    res.status(200).json({
      success: true,
      status,
      summary,
    });
  } catch (error) {
    console.error("Error getting sync status:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get sync status",
      error: error.message,
    });
  }
});

// @desc    Trigger full data sync
// @route   POST /api/data-sync/sync-all
// @access  Private
router.post("/sync-all", async (req, res) => {
  try {
    const { forceRefresh = false } = req.body;

    // Check if sync is already running
    const status = dataSyncService.getSyncStatus();
    if (status.isRunning && !forceRefresh) {
      return res.status(409).json({
        success: false,
        message: "Data sync is already running",
        status,
      });
    }

    // Start sync in background
    const syncPromise = dataSyncService.syncAllPluginData(forceRefresh);

    // Return immediate response
    res.status(202).json({
      success: true,
      message: "Data sync started",
      status: dataSyncService.getSyncStatus(),
    });

    // Handle sync completion/error in background
    try {
      const results = await syncPromise;
      console.log("Data sync completed:", results);
    } catch (error) {
      console.error("Data sync failed:", error);
    }
  } catch (error) {
    console.error("Error starting data sync:", error);
    res.status(500).json({
      success: false,
      message: "Failed to start data sync",
      error: error.message,
    });
  }
});

// @desc    Sync single plugin
// @route   POST /api/data-sync/sync-plugin/:slug
// @access  Private
router.post("/sync-plugin/:slug", async (req, res) => {
  try {
    const { slug } = req.params;

    const result = await dataSyncService.syncSinglePlugin(slug);

    res.status(200).json({
      success: true,
      message: `Plugin ${slug} synced successfully`,
      data: result,
    });
  } catch (error) {
    console.error(`Error syncing plugin ${req.params.slug}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to sync plugin ${req.params.slug}`,
      error: error.message,
    });
  }
});

// @desc    Get all plugins with cached data
// @route   GET /api/data-sync/plugins
// @access  Private
router.get("/plugins", async (req, res) => {
  try {
    const {
      search,
      status,
      page = 1,
      limit = 50,
      sortBy = "downloads",
      sortOrder = "desc",
      includeHistory = false,
    } = req.query;

    // Build query
    let query = { trackingEnabled: true };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { slug: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (status) {
      query.status = status;
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Execute query with pagination
    const plugins = await Plugin.find(query)
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select(
        "name slug downloads rating pluginRank supportTickets lastDataFetch lastReleaseDate version activeInstalls ratingCount"
      );

    // Get total count for pagination
    const total = await Plugin.countDocuments(query);

    // Include download history if requested
    let pluginsWithHistory = plugins;
    if (includeHistory === "true") {
      pluginsWithHistory = await Promise.all(
        plugins.map(async (plugin) => {
          const history = await DownloadHistory.find({
            pluginId: plugin._id,
          })
            .sort({ date: -1 })
            .limit(30)
            .select("date downloads rating pluginRank");

          return {
            ...plugin.toObject(),
            downloadHistory: history,
          };
        })
      );
    }

    res.status(200).json({
      success: true,
      data: pluginsWithHistory,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
      meta: {
        lastSync: dataSyncService.getSyncStatus().lastSync,
        cacheValid: true,
      },
    });
  } catch (error) {
    console.error("Error fetching cached plugins:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch plugins",
      error: error.message,
    });
  }
});

// @desc    Get download statistics for all plugins
// @route   GET /api/data-sync/download-stats
// @access  Private
router.get("/download-stats", async (req, res) => {
  try {
    const { days = 30, pluginSlug } = req.query;

    let matchQuery = {};
    if (pluginSlug) {
      matchQuery.slug = pluginSlug;
    }

    // Get date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    matchQuery.date = {
      $gte: startDate,
      $lte: endDate,
    };

    const downloadStats = await DownloadHistory.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: "$slug",
          data: {
            $push: {
              date: "$date",
              downloads: "$downloads",
              rating: "$rating",
              pluginRank: "$pluginRank",
              activeInstalls: "$activeInstalls",
            },
          },
          totalDownloads: { $sum: "$downloads" },
          avgRating: { $avg: "$rating" },
          latestRank: { $last: "$pluginRank" },
        },
      },
      { $sort: { totalDownloads: -1 } },
    ]);

    // Transform to object format
    const statsMap = {};
    downloadStats.forEach((stat) => {
      statsMap[stat._id] = {
        data: stat.data,
        summary: {
          totalDownloads: stat.totalDownloads,
          avgRating: stat.avgRating,
          latestRank: stat.latestRank,
        },
      };
    });

    res.status(200).json({
      success: true,
      data: statsMap,
      meta: {
        days: parseInt(days),
        startDate,
        endDate,
        pluginCount: Object.keys(statsMap).length,
      },
    });
  } catch (error) {
    console.error("Error fetching download statistics:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch download statistics",
      error: error.message,
    });
  }
});

// @desc    Start automatic sync
// @route   POST /api/data-sync/auto-sync/start
// @access  Private (Admin only)
router.post("/auto-sync/start", async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin" && req.user.role !== "superadmin") {
      return res.status(403).json({
        success: false,
        message: "Access denied. Admin privileges required.",
      });
    }

    const { intervalMinutes = 60 } = req.body;

    dataSyncService.startAutoSync(intervalMinutes);

    res.status(200).json({
      success: true,
      message: `Automatic sync started with ${intervalMinutes} minute interval`,
      status: dataSyncService.getSyncStatus(),
    });
  } catch (error) {
    console.error("Error starting auto sync:", error);
    res.status(500).json({
      success: false,
      message: "Failed to start automatic sync",
      error: error.message,
    });
  }
});

// @desc    Stop automatic sync
// @route   POST /api/data-sync/auto-sync/stop
// @access  Private (Admin only)
router.post("/auto-sync/stop", async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin" && req.user.role !== "superadmin") {
      return res.status(403).json({
        success: false,
        message: "Access denied. Admin privileges required.",
      });
    }

    dataSyncService.stopAutoSync();

    res.status(200).json({
      success: true,
      message: "Automatic sync stopped",
      status: dataSyncService.getSyncStatus(),
    });
  } catch (error) {
    console.error("Error stopping auto sync:", error);
    res.status(500).json({
      success: false,
      message: "Failed to stop automatic sync",
      error: error.message,
    });
  }
});

module.exports = router;
