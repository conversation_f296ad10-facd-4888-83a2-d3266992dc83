const Plugin = require("../models/Plugin");
const DownloadHistory = require("../models/DownloadHistory");
const { fetchPluginData } = require("./wordpressApi");
const axios = require("axios");

class DataSyncService {
  constructor() {
    this.isRunning = false;
    this.lastSync = null;
    this.syncInterval = null;
  }

  /**
   * Sync all plugin data from WordPress.org
   * @param {boolean} forceRefresh - Force refresh even if recently synced
   * @returns {Object} Sync results
   */
  async syncAllPluginData(forceRefresh = false) {
    if (this.isRunning && !forceRefresh) {
      throw new Error("Data sync is already running");
    }

    this.isRunning = true;
    const startTime = Date.now();
    const results = {
      success: 0,
      errors: 0,
      plugins: [],
      downloadData: {},
      errors: [],
    };

    try {
      console.log("Starting data sync for all plugins...");

      // Get all active plugins
      const plugins = await Plugin.find({ trackingEnabled: true });
      console.log(`Found ${plugins.length} plugins to sync`);

      // Sync each plugin
      for (const plugin of plugins) {
        try {
          const syncResult = await this.syncSinglePlugin(plugin.slug);
          results.plugins.push(syncResult);
          results.success++;
        } catch (error) {
          console.error(`Error syncing plugin ${plugin.slug}:`, error);
          results.errors.push({
            plugin: plugin.slug,
            error: error.message,
          });
          results.errors++;
        }
      }

      // Fetch and store download statistics
      try {
        const downloadStats = await this.syncDownloadStatistics();
        results.downloadData = downloadStats;
      } catch (error) {
        console.error("Error syncing download statistics:", error);
        results.errors.push({
          operation: "download_statistics",
          error: error.message,
        });
      }

      this.lastSync = new Date();
      const duration = Date.now() - startTime;

      console.log(`Data sync completed in ${duration}ms. Success: ${results.success}, Errors: ${results.errors}`);

      return {
        ...results,
        duration,
        timestamp: this.lastSync,
      };
    } catch (error) {
      console.error("Critical error during data sync:", error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Sync a single plugin's data
   * @param {string} slug - Plugin slug
   * @returns {Object} Plugin data
   */
  async syncSinglePlugin(slug) {
    try {
      console.log(`Syncing plugin: ${slug}`);

      // Find the plugin in database
      const plugin = await Plugin.findOne({ slug });
      if (!plugin) {
        throw new Error(`Plugin ${slug} not found in database`);
      }

      // Fetch fresh data from WordPress.org
      const wpData = await fetchPluginData(slug);
      if (!wpData) {
        throw new Error(`No data received from WordPress.org for ${slug}`);
      }

      // Update plugin with fresh data
      Object.assign(plugin, wpData);
      plugin.lastDataFetch = new Date();
      await plugin.save();

      console.log(`Successfully synced plugin: ${slug}`);
      return {
        slug,
        name: plugin.name,
        downloads: plugin.downloads,
        rating: plugin.rating,
        pluginRank: plugin.pluginRank,
        lastUpdated: plugin.lastDataFetch,
      };
    } catch (error) {
      console.error(`Error syncing plugin ${slug}:`, error);
      throw error;
    }
  }

  /**
   * Sync download statistics for all plugins
   * @returns {Object} Download statistics
   */
  async syncDownloadStatistics() {
    try {
      console.log("Syncing download statistics...");

      const plugins = await Plugin.find({ trackingEnabled: true });
      const downloadStats = {};

      for (const plugin of plugins) {
        try {
          // Fetch download stats from WordPress.org API
          const response = await axios.get(
            `https://api.wordpress.org/stats/plugin/1.0/downloads.php?slug=${plugin.slug}&limit=365`
          );

          if (response.data) {
            downloadStats[plugin.slug] = response.data;

            // Store daily download data in DownloadHistory
            await this.storeDownloadHistory(plugin, response.data);
          }
        } catch (error) {
          console.error(`Error fetching download stats for ${plugin.slug}:`, error);
        }
      }

      console.log(`Download statistics synced for ${Object.keys(downloadStats).length} plugins`);
      return downloadStats;
    } catch (error) {
      console.error("Error syncing download statistics:", error);
      throw error;
    }
  }

  /**
   * Store download history for a plugin
   * @param {Object} plugin - Plugin document
   * @param {Object} downloadData - Download data from WordPress.org
   */
  async storeDownloadHistory(plugin, downloadData) {
    try {
      if (!downloadData || typeof downloadData !== "object") {
        return;
      }

      // Process each date in the download data
      for (const [dateStr, downloads] of Object.entries(downloadData)) {
        const date = new Date(dateStr);
        
        // Skip invalid dates or future dates
        if (isNaN(date.getTime()) || date > new Date()) {
          continue;
        }

        // Check if record already exists
        const existingRecord = await DownloadHistory.findOne({
          pluginId: plugin._id,
          date: date,
        });

        const historyData = {
          pluginId: plugin._id,
          slug: plugin.slug,
          date: date,
          downloads: parseInt(downloads) || 0,
          activeInstalls: plugin.activeInstalls || 0,
          rating: plugin.rating || 0,
          pluginRank: plugin.pluginRank || 0,
          unresolvedTopics: plugin.supportTickets?.unresolvedTopics || 0,
        };

        if (existingRecord) {
          // Update existing record
          await DownloadHistory.findByIdAndUpdate(existingRecord._id, historyData);
        } else {
          // Create new record
          await DownloadHistory.create(historyData);
        }
      }
    } catch (error) {
      console.error(`Error storing download history for ${plugin.slug}:`, error);
    }
  }

  /**
   * Get sync status
   * @returns {Object} Sync status
   */
  getSyncStatus() {
    return {
      isRunning: this.isRunning,
      lastSync: this.lastSync,
      nextSync: this.syncInterval ? new Date(Date.now() + this.syncInterval) : null,
    };
  }

  /**
   * Start automatic sync interval
   * @param {number} intervalMinutes - Interval in minutes
   */
  startAutoSync(intervalMinutes = 60) {
    if (this.syncInterval) {
      this.stopAutoSync();
    }

    const intervalMs = intervalMinutes * 60 * 1000;
    this.syncInterval = setInterval(async () => {
      try {
        console.log("Starting automatic data sync...");
        await this.syncAllPluginData();
      } catch (error) {
        console.error("Error in automatic sync:", error);
      }
    }, intervalMs);

    console.log(`Automatic sync started with ${intervalMinutes} minute interval`);
  }

  /**
   * Stop automatic sync
   */
  stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log("Automatic sync stopped");
    }
  }

  /**
   * Get cached data summary
   * @returns {Object} Data summary
   */
  async getDataSummary() {
    try {
      const [pluginCount, historyCount, lastPlugin, lastHistory] = await Promise.all([
        Plugin.countDocuments({ trackingEnabled: true }),
        DownloadHistory.countDocuments(),
        Plugin.findOne({ trackingEnabled: true }).sort({ lastDataFetch: -1 }),
        DownloadHistory.findOne().sort({ createdAt: -1 }),
      ]);

      return {
        totalPlugins: pluginCount,
        totalHistoryRecords: historyCount,
        lastPluginUpdate: lastPlugin?.lastDataFetch,
        lastHistoryUpdate: lastHistory?.createdAt,
        syncStatus: this.getSyncStatus(),
      };
    } catch (error) {
      console.error("Error getting data summary:", error);
      throw error;
    }
  }
}

// Create singleton instance
const dataSyncService = new DataSyncService();

module.exports = dataSyncService;
