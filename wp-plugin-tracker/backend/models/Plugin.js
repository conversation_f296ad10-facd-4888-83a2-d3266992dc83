const mongoose = require('mongoose');

const pluginSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  version: {
    type: String,
    default: '1.0.0'
  },
  author: {
    type: String,
    default: 'Unknown'
  },
  description: {
    type: String,
    default: ''
  },
  shortDescription: {
    type: String,
    default: ''
  },
  downloadCount: {
    type: Number,
    default: 0
  },
  activeInstalls: {
    type: Number,
    default: 0
  },
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  numRatings: {
    type: Number,
    default: 0
  },
  currentRank: {
    type: Number,
    default: null
  },
  previousRank: {
    type: Number,
    default: null
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  lastReleaseDate: {
    type: Date,
    default: null
  },
  requires: {
    type: String,
    default: '5.0'
  },
  tested: {
    type: String,
    default: '6.4'
  },
  requiresPhp: {
    type: String,
    default: '7.4'
  },
  tags: [{
    type: String,
    trim: true
  }],
  screenshots: [{
    src: String,
    caption: String
  }],
  banners: {
    low: String,
    high: String
  },
  icons: {
    '1x': String,
    '2x': String,
    svg: String
  },
  contributors: [{
    type: String,
    trim: true
  }],
  donateLink: {
    type: String,
    default: ''
  },
  homepage: {
    type: String,
    default: ''
  },
  sections: {
    description: String,
    installation: String,
    faq: String,
    screenshots: String,
    changelog: String,
    reviews: String,
    other_notes: String
  },
  downloadHistory: [{
    date: {
      type: Date,
      required: true
    },
    downloads: {
      type: Number,
      required: true
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  isTracked: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
pluginSchema.index({ slug: 1 });
pluginSchema.index({ name: 1 });
pluginSchema.index({ currentRank: 1 });
pluginSchema.index({ downloadCount: -1 });
pluginSchema.index({ rating: -1 });
pluginSchema.index({ lastUpdated: -1 });
pluginSchema.index({ isActive: 1, isTracked: 1 });

// Virtual for rank change
pluginSchema.virtual('rankChange').get(function() {
  if (this.previousRank && this.currentRank) {
    return this.previousRank - this.currentRank;
  }
  return 0;
});

// Virtual for days since last release
pluginSchema.virtual('daysSinceLastRelease').get(function() {
  if (this.lastReleaseDate) {
    const now = new Date();
    const releaseDate = new Date(this.lastReleaseDate);
    const diffTime = Math.abs(now - releaseDate);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  return null;
});

// Static method to find by slug
pluginSchema.statics.findBySlug = function(slug) {
  return this.findOne({ slug: slug.toLowerCase() });
};

// Instance method to update download count
pluginSchema.methods.updateDownloadCount = function(newCount) {
  this.downloadCount = newCount;
  this.lastUpdated = new Date();
  return this.save();
};

// Instance method to update rank
pluginSchema.methods.updateRank = function(newRank) {
  this.previousRank = this.currentRank;
  this.currentRank = newRank;
  this.lastUpdated = new Date();
  return this.save();
};

module.exports = mongoose.model('Plugin', pluginSchema);
