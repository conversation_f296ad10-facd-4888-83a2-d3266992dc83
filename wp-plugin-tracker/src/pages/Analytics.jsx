import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import {
  FiTrendingUp,
  FiDownload,
  FiStar,
  FiRefreshCw,
  FiFilter,
  FiPackage,
  FiCalendar,
} from "react-icons/fi";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../styles/datepicker.css";
import { useData } from "../contexts/DataContext";
import { wpAPI } from "../services/api";
import { toast } from "react-hot-toast";

const Analytics = () => {
  const location = useLocation();
  const { plugins, loading, refreshAllData } = useData();
  const [refreshing, setRefreshing] = useState(false);

  // Chart data states
  const [rankChartData, setRankChartData] = useState([]);
  const [downloadLogsData, setDownloadLogsData] = useState([]);
  const [reviewStatsData, setReviewStatsData] = useState([]);
  const [ratingData, setRatingData] = useState(null);
  const [chartLoading, setChartLoading] = useState(false);
  const [rankChangeData, setRankChangeData] = useState([]);

  // Helper function to get display name (first part only)
  const getDisplayName = (fullName) => {
    if (!fullName) return "";

    // Split by common separators and take the first part
    const separators = [" –", " -", " |", " :", " ("];
    let displayName = fullName;

    for (const separator of separators) {
      if (displayName.includes(separator)) {
        displayName = displayName.split(separator)[0];
        break;
      }
    }

    return displayName.trim();
  };

  // Date formatting utilities
  const formatDateToDDMMYYYY = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const parseDateFromDDMMYYYY = (dateString) => {
    if (!dateString) return "";
    const [day, month, year] = dateString.split("-");
    if (day && month && year) {
      return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
    }
    return "";
  };

  const updateDateRangeText = () => {
    if (dateRange.startDate && dateRange.endDate) {
      const startFormatted = formatDateToDDMMYYYY(dateRange.startDate);
      const endFormatted = formatDateToDDMMYYYY(dateRange.endDate);
      setDateRangeText(`${startFormatted} to ${endFormatted}`);
    } else {
      setDateRangeText("");
    }
  };

  // Filter states
  const [selectedPlugin, setSelectedPlugin] = useState("");
  const [dateRange, setDateRange] = useState({
    startDate: "",
    endDate: "",
  });
  const [dateRangeText, setDateRangeText] = useState("");
  const [dateRangePicker, setDateRangePicker] = useState([null, null]);
  const [startDate, endDate] = dateRangePicker;

  // Auto-refresh states
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState(null);

  useEffect(() => {
    fetchPlugins();
    // Set default date range to current month on initial load
    setDefaultDateRange();
  }, []);

  // Handle URL parameters for plugin selection
  useEffect(() => {
    if (plugins.length > 0) {
      const searchParams = new URLSearchParams(location.search);
      const pluginParam = searchParams.get("plugin");

      if (pluginParam) {
        // Check if the plugin ID exists in the plugins list
        const pluginExists = plugins.find((p) => p._id === pluginParam);
        if (pluginExists) {
          setSelectedPlugin(pluginParam);
          console.log("Auto-selected plugin from URL:", pluginExists.name);
        } else {
          console.warn("Plugin ID from URL not found:", pluginParam);
        }
      }
    }
  }, [plugins, location.search]);

  useEffect(() => {
    if (selectedPlugin) {
      fetchAllChartData();
    }
  }, [selectedPlugin, dateRange]);

  useEffect(() => {
    updateDateRangeText();
  }, [dateRange]);

  // Auto-refresh effect
  useEffect(() => {
    let interval;
    if (autoRefresh && selectedPlugin) {
      // Auto-refresh every 5 minutes
      interval = setInterval(async () => {
        console.log("Auto-refreshing analytics data...");
        try {
          await wpAPI.storeDownloadData();
          await fetchAllChartData();
          setLastRefreshTime(new Date());
          toast.success("Data auto-refreshed successfully", { duration: 2000 });
        } catch (error) {
          console.error("Auto-refresh failed:", error);
          toast.error("Auto-refresh failed", { duration: 2000 });
        }
      }, 5 * 60 * 1000); // 5 minutes
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoRefresh, selectedPlugin]);

  // Remove fetchPlugins since we're using centralized data

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      toast("Refreshing download data and checking for missing entries...", {
        icon: "🔄",
        duration: 3000,
      });

      // Use centralized refresh
      await refreshAllData();

      if (selectedPlugin) {
        await fetchAllChartData();
        toast.success("Analytics data updated with latest information!");
      } else {
        toast.success("Data refreshed! Select a plugin to view analytics.");
      }

      // Update last refresh time
      setLastRefreshTime(new Date());
    } catch (error) {
      console.error("Error refreshing analytics:", error);
      toast.error(
        "Failed to refresh analytics: " +
          (error.response?.data?.message || error.message)
      );
    } finally {
      setRefreshing(false);
    }
  };

  const fetchChartData = async () => {
    await fetchAllChartData();
  };

  const fetchRatingData = async (pluginSlug) => {
    try {
      const response = await fetch(
        `https://api.wordpress.org/plugins/info/1.0/${pluginSlug}.json`
      );
      const data = await response.json();

      if (data && data.ratings) {
        const ratingBreakdown = {
          five: parseInt(data.ratings["5"]) || 0,
          four: parseInt(data.ratings["4"]) || 0,
          three: parseInt(data.ratings["3"]) || 0,
          two: parseInt(data.ratings["2"]) || 0,
          one: parseInt(data.ratings["1"]) || 0,
        };

        setRatingData({
          rating: data.rating
            ? (parseFloat(data.rating) / 20).toFixed(1)
            : "0.0",
          ratingCount: parseInt(data.num_ratings) || 0,
          ratingBreakdown: ratingBreakdown,
        });

        console.log("Rating data fetched for analytics:", {
          plugin: pluginSlug,
          rating: data.rating
            ? (parseFloat(data.rating) / 20).toFixed(1)
            : "0.0",
          ratingCount: parseInt(data.num_ratings) || 0,
          ratingBreakdown: ratingBreakdown,
        });
      }
    } catch (error) {
      console.error("Error fetching rating data:", error);
      setRatingData(null);
    }
  };

  const fetchRankChangeData = async (pluginSlug) => {
    try {
      // Find the plugin ID from the selected plugin
      const selectedPluginData = plugins.find((p) => p.slug === pluginSlug);
      if (!selectedPluginData) {
        console.error("Plugin not found for rank data:", pluginSlug);
        setRankChangeData([]);
        return;
      }

      // Get the last 15 days of rank data
      const endDate = new Date();
      endDate.setDate(endDate.getDate() - 1); // Yesterday
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 15); // 15 days ago

      const params = {
        type: "rank_chart",
        pluginId: selectedPluginData._id,
        startDate: startDate.toISOString().split("T")[0],
        endDate: endDate.toISOString().split("T")[0],
      };

      console.log("Fetching rank change data with params:", params);

      const response = await wpAPI.getAnalytics(params);

      if (
        response.data.success &&
        response.data.data &&
        response.data.data.length > 0
      ) {
        // Transform the data for the line chart
        const rankData = response.data.data
          .map((item) => ({
            date: new Date(item.date).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
            }),
            originalDate: item.date,
            rank: item.rank || 0,
            pluginName: item.pluginName,
          }))
          .sort((a, b) => new Date(a.originalDate) - new Date(b.originalDate));

        setRankChangeData(rankData);
        console.log("Rank change data fetched:", rankData);
      } else {
        setRankChangeData([]);
        console.log("No rank change data available");
      }
    } catch (error) {
      console.error("Error fetching rank change data:", error);
      setRankChangeData([]);
    }
  };

  const fetchAllChartData = async () => {
    // Check if we have the necessary data to proceed
    if (!selectedPlugin) {
      console.log("No plugin selected, skipping chart data fetch");
      setChartLoading(false);
      return;
    }

    if (!plugins || plugins.length === 0) {
      console.log("Plugins not loaded yet, skipping chart data fetch");
      setChartLoading(false);
      return;
    }

    // Chart loading is already set by handleSubmitDateRange or will be set here for initial load
    if (!chartLoading) {
      setChartLoading(true);
    }

    try {
      console.log("Starting chart data fetch for plugin:", selectedPlugin);
      console.log(
        "Available plugins:",
        plugins.map((p) => ({ id: p._id, name: p.name, slug: p.slug }))
      );

      // Find the selected plugin slug from the plugins list
      const selectedPluginData = plugins.find((p) => p._id === selectedPlugin);
      if (!selectedPluginData) {
        console.error("No plugin selected or plugin not found");
        console.error("Selected plugin ID:", selectedPlugin);
        console.error(
          "Available plugin IDs:",
          plugins.map((p) => p._id)
        );
        toast.error("Please select a valid plugin");
        setChartLoading(false);
        return;
      }

      console.log("Found plugin data:", selectedPluginData);

      // Fetch rating data for the selected plugin
      await fetchRatingData(selectedPluginData.slug);

      // Set date range to exclude today (show up to yesterday)
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(23, 59, 59, 999);

      // Fetch rank change data for the last 15 days
      await fetchRankChangeData(selectedPluginData.slug);

      const downloadParams = {
        limit: 1000,
        pluginSlug: selectedPluginData.slug,
        ...(dateRange.startDate && { startDate: dateRange.startDate }),
        // If endDate is provided, use it, but cap it at yesterday
        endDate: dateRange.endDate
          ? new Date(dateRange.endDate) > yesterday
            ? yesterday.toISOString().split("T")[0]
            : dateRange.endDate
          : yesterday.toISOString().split("T")[0],
      };

      console.log("Fetching download data with params:", downloadParams);
      console.log("Data will be shown up to:", downloadParams.endDate);

      // Validate required parameters
      if (!downloadParams.pluginSlug) {
        throw new Error("Plugin slug is required");
      }

      // Fetch download data from the Download Data API
      const downloadResponse = await wpAPI.getDownloadData(downloadParams);

      console.log("Download data response:", downloadResponse);
      console.log("Download data response data:", downloadResponse.data);

      if (downloadResponse.data.data && downloadResponse.data.data.length > 0) {
        const pluginData = downloadResponse.data.data[0]; // Should be only one plugin
        const downloadHistory = pluginData.downloadHistory || [];

        // Filter out today's data (extra safety check)
        const filteredHistory = downloadHistory.filter((record) => {
          const recordDate = new Date(record.date);
          return recordDate <= yesterday;
        });

        // Transform download history into chart format
        const chartData = filteredHistory.map((record) => ({
          date: record.date,
          downloads: record.downloads,
          pluginName: getDisplayName(pluginData.pluginName),
          pluginSlug: pluginData.slug,
        }));

        // Sort by date (oldest first for proper chart display)
        chartData.sort((a, b) => new Date(a.date) - new Date(b.date));

        console.log("Processed chart data (up to yesterday):", chartData);

        setDownloadLogsData(chartData);

        // For now, clear other chart data since we're focusing on download data
        setRankChartData([]);
        setReviewStatsData([]);

        if (chartData.length === 0) {
          toast(
            "No download data available for the selected plugin and date range (excluding today).",
            {
              icon: "ℹ️",
              duration: 4000,
            }
          );
        } else {
          // Silently load data without toast message
          console.log(
            `Loaded ${
              chartData.length
            } days of download data for ${getDisplayName(
              pluginData.pluginName
            )}`
          );
        }
      } else {
        setDownloadLogsData([]);
        setRankChartData([]);
        setReviewStatsData([]);
        setRankChangeData([]);

        toast(
          "No download data available for the selected plugin and date range.",
          {
            icon: "ℹ️",
            duration: 4000,
          }
        );
      }
    } catch (error) {
      console.error("Error fetching chart data:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config,
      });

      // Clear chart data on error
      setDownloadLogsData([]);
      setRankChartData([]);
      setReviewStatsData([]);
      setRatingData(null);
      setRankChangeData([]);

      let errorMessage = "Failed to refresh dashboard data: ";
      if (error.response?.status === 401) {
        errorMessage += "Authentication required. Please log in again.";
      } else if (error.response?.status === 404) {
        errorMessage += "Data not found for the selected plugin.";
      } else if (error.response?.status === 500) {
        errorMessage += "Server error. Please try again later.";
      } else if (error.response?.data?.message) {
        errorMessage += error.response.data.message;
      } else if (error.message) {
        errorMessage += error.message;
      } else {
        errorMessage += "Unknown error occurred. Please try again.";
      }

      toast.error(errorMessage);
    } finally {
      // Always clear loading state
      setChartLoading(false);
    }
  };

  const handleDateRangeChange = (field, value) => {
    setDateRange((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDateRangeTextChange = (value) => {
    setDateRangeText(value);

    // Try to parse the date range text
    const parts = value.split(" to ");
    if (parts.length === 2) {
      const startDateStr = parseDateFromDDMMYYYY(parts[0].trim());
      const endDateStr = parseDateFromDDMMYYYY(parts[1].trim());

      if (startDateStr && endDateStr) {
        setDateRange({
          startDate: startDateStr,
          endDate: endDateStr,
        });

        // Update date picker states
        setDateRangePicker([new Date(startDateStr), new Date(endDateStr)]);
      }
    }
  };

  const handleDatePickerChange = (update) => {
    setDateRangePicker(update);
    const [start, end] = update;

    if (start && end) {
      // Both dates selected - update display text only (don't fetch data yet)
      const startFormatted = formatDateToDDMMYYYY(
        start.toISOString().split("T")[0]
      );
      const endFormatted = formatDateToDDMMYYYY(
        end.toISOString().split("T")[0]
      );
      setDateRangeText(`${startFormatted} to ${endFormatted}`);
    } else if (!start && !end) {
      // Clear display text when both dates are cleared
      setDateRangeText("");
    } else {
      // Only one date selected - clear display text
      setDateRangeText("");
    }
  };

  const handleSubmitDateRange = () => {
    const [start, end] = dateRangePicker;

    if (start && end && selectedPlugin) {
      // Set chart loading state
      setChartLoading(true);

      // Update internal date range and fetch data
      const startDateStr = start.toISOString().split("T")[0];
      const endDateStr = end.toISOString().split("T")[0];

      setDateRange({
        startDate: startDateStr,
        endDate: endDateStr,
      });

      // Data will be fetched automatically via useEffect when dateRange changes
    }
  };

  const setDefaultDateRange = () => {
    // Set to current month - from 1st of current month to yesterday (or last available data date)
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    // Start date: 1st of current month
    const startDateObj = new Date(currentYear, currentMonth, 1);

    // End date: Yesterday (exclude today as data might not be available)
    const endDateObj = new Date();
    endDateObj.setDate(endDateObj.getDate() - 1);

    const newDateRange = {
      startDate: startDateObj.toISOString().split("T")[0],
      endDate: endDateObj.toISOString().split("T")[0],
    };

    setDateRange(newDateRange);

    // Update date picker states and display text
    setDateRangePicker([startDateObj, endDateObj]);

    const startFormatted = formatDateToDDMMYYYY(newDateRange.startDate);
    const endFormatted = formatDateToDDMMYYYY(newDateRange.endDate);
    setDateRangeText(`${startFormatted} to ${endFormatted}`);
  };

  const handlePluginChange = (pluginId) => {
    setSelectedPlugin(pluginId);
    if (pluginId) {
      // Set default date range to current month when plugin is selected
      setDefaultDateRange();
    } else {
      // Clear date range when no plugin is selected
      setDateRange({ startDate: "", endDate: "" });
      setDateRangeText("");
      setDateRangePicker([null, null]);
    }
  };

  // Chart Components
  const RankAreaChart = ({ data }) => {
    if (!data || data.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No rank data available</p>
        </div>
      );
    }

    // Process data for area chart visualization
    const processedData = data.reduce((acc, item) => {
      const dateKey = new Date(item.date).toLocaleDateString();
      if (!acc[dateKey]) {
        acc[dateKey] = { date: dateKey, plugins: [] };
      }
      acc[dateKey].plugins.push({
        name: getDisplayName(item.pluginName),
        rank: item.rank,
      });
      return acc;
    }, {});

    const chartData = Object.values(processedData).sort(
      (a, b) => new Date(a.date) - new Date(b.date)
    );

    return (
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-gray-900">
          Plugin Rank Trends (Area Chart)
        </h4>
        <div className="bg-gray-50 rounded-lg p-6">
          <div className="relative h-64">
            {/* Simple area chart visualization */}
            <svg width="100%" height="100%" className="overflow-visible">
              {chartData.map((dayData, dayIndex) => {
                const x = (dayIndex / (chartData.length - 1)) * 100;
                return dayData.plugins.map((plugin, pluginIndex) => {
                  const y = ((50 - plugin.rank) / 50) * 100; // Invert rank for visual
                  const color = `hsl(${pluginIndex * 60}, 70%, 50%)`;

                  return (
                    <g key={`${dayIndex}-${pluginIndex}`}>
                      {/* Area fill */}
                      <path
                        d={`M ${x}% 100% L ${x}% ${Math.max(y, 0)}% L ${
                          x + 2
                        }% ${Math.max(y, 0)}% L ${x + 2}% 100% Z`}
                        fill={color}
                        fillOpacity="0.3"
                      />
                      {/* Data point */}
                      <circle
                        cx={`${x}%`}
                        cy={`${Math.max(y, 0)}%`}
                        r="4"
                        fill={color}
                        className="hover:r-6 transition-all cursor-pointer"
                      >
                        <title>{`${plugin.name}: Rank #${plugin.rank} on ${dayData.date}`}</title>
                      </circle>
                      {/* Data label */}
                      <text
                        x={`${x}%`}
                        y={`${Math.max(y - 8, 5)}%`}
                        textAnchor="middle"
                        className="text-xs font-medium fill-gray-700"
                        style={{ fontSize: "10px" }}
                      >
                        #{plugin.rank}
                      </text>
                    </g>
                  );
                });
              })}
            </svg>
          </div>

          {/* X-axis labels (dates) */}
          <div className="mt-2 flex justify-between text-xs text-gray-500">
            {chartData.map((dayData, index) => {
              if (
                index === 0 ||
                index === chartData.length - 1 ||
                index === Math.floor(chartData.length / 2)
              ) {
                return (
                  <span key={index} className="text-center">
                    {dayData.date}
                  </span>
                );
              }
              return null;
            })}
          </div>

          {/* Legend */}
          <div className="mt-4 flex flex-wrap gap-4">
            {data
              .reduce((acc, item) => {
                const displayName = getDisplayName(item.pluginName);
                if (!acc.find((p) => p.name === displayName)) {
                  acc.push({
                    name: displayName,
                    color: `hsl(${acc.length * 60}, 70%, 50%)`,
                  });
                }
                return acc;
              }, [])
              .map((plugin, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: plugin.color }}
                  />
                  <span className="text-sm text-gray-700">{plugin.name}</span>
                </div>
              ))}
          </div>
        </div>
      </div>
    );
  };

  const RankChangeLineChart = ({ data }) => {
    console.log("Rank change chart data:", data);

    if (!data || data.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No rank change data available</p>
          <p className="text-xs text-gray-400 mt-2">
            Data length: {data?.length || 0}
          </p>
          <div className="mt-4 p-4 bg-blue-50 rounded">
            <p className="text-sm text-blue-700">
              <strong>Rank change data shows:</strong>
            </p>
            <ul className="text-sm text-blue-600 mt-2 list-disc list-inside">
              <li>Plugin ranking trends over the last 15 days</li>
              <li>Lower rank numbers indicate better performance</li>
              <li>Data is updated daily from WordPress.org</li>
            </ul>
          </div>
        </div>
      );
    }

    // Sort data by date for proper chart display
    const sortedData = [...data].sort(
      (a, b) =>
        new Date(a.originalDate || a.date) - new Date(b.originalDate || b.date)
    );

    const maxRank = Math.max(...sortedData.map((d) => d.rank));
    const minRank = Math.min(...sortedData.map((d) => d.rank));
    const avgRank = Math.round(
      sortedData.reduce((sum, d) => sum + d.rank, 0) / sortedData.length
    );

    // Calculate trend
    const firstRank = sortedData[0]?.rank || 0;
    const lastRank = sortedData[sortedData.length - 1]?.rank || 0;
    const rankChange = firstRank - lastRank; // Positive means improvement (lower rank number)
    const trendDirection =
      rankChange > 0 ? "improved" : rankChange < 0 ? "declined" : "stable";

    return (
      <div className="space-y-4">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
          <div>
            <h4 className="text-lg font-semibold text-gray-900">
              Plugin Rank Change Trend (Last 15 Days)
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              Lower rank numbers indicate better performance
            </p>
            <div className="text-sm text-gray-600 mt-1">
              {sortedData.length} days • Rank {minRank} - {maxRank} • Trend:{" "}
              {trendDirection}
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-4 gap-4 mb-4">
          <div className="bg-blue-50 p-3 rounded-lg text-center">
            <div className="text-lg font-semibold text-blue-600">{minRank}</div>
            <div className="text-xs text-blue-500">Best Rank</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="text-lg font-semibold text-green-600">
              {avgRank}
            </div>
            <div className="text-xs text-green-500">Average Rank</div>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg text-center">
            <div className="text-lg font-semibold text-gray-600">{maxRank}</div>
            <div className="text-xs text-gray-500">Worst Rank</div>
          </div>
          <div
            className={`p-3 rounded-lg text-center ${
              rankChange > 0
                ? "bg-green-50"
                : rankChange < 0
                ? "bg-red-50"
                : "bg-gray-50"
            }`}
          >
            <div
              className={`text-lg font-semibold ${
                rankChange > 0
                  ? "text-green-600"
                  : rankChange < 0
                  ? "text-red-600"
                  : "text-gray-600"
              }`}
            >
              {rankChange > 0 ? `+${rankChange}` : rankChange}
            </div>
            <div
              className={`text-xs ${
                rankChange > 0
                  ? "text-green-500"
                  : rankChange < 0
                  ? "text-red-500"
                  : "text-gray-500"
              }`}
            >
              Rank Change
            </div>
          </div>
        </div>

        {/* Line Chart */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div
            className="relative w-full"
            style={{ height: "280px", paddingBottom: "40px" }}
          >
            {/* Grid Lines */}
            <div className="absolute inset-0" style={{ paddingBottom: "40px" }}>
              {/* Horizontal Grid Lines */}
              {[0, 25, 50, 75, 100].map((percentage) => (
                <div
                  key={`h-${percentage}`}
                  className="absolute w-full border-t border-gray-300/50"
                  style={{
                    bottom: `${(percentage / 100) * 240 + 40}px`,
                  }}
                />
              ))}

              {/* Vertical Grid Lines */}
              {sortedData.map((_, index) => (
                <div
                  key={`v-${index}`}
                  className="absolute h-full border-l border-gray-300/30"
                  style={{
                    left: `${((index + 0.5) / sortedData.length) * 100}%`,
                  }}
                />
              ))}
            </div>

            {/* Line Chart SVG */}
            <svg
              width="100%"
              height="240"
              className="absolute top-0 left-0"
              style={{ paddingBottom: "40px" }}
            >
              {/* Line Path */}
              <path
                d={sortedData
                  .map((item, index) => {
                    const x = ((index + 0.5) / sortedData.length) * 100;
                    const y =
                      100 -
                      ((item.rank - minRank) / (maxRank - minRank || 1)) * 80;
                    return `${index === 0 ? "M" : "L"} ${x}% ${y}%`;
                  })
                  .join(" ")}
                stroke="#3B82F6"
                strokeWidth="3"
                fill="none"
                className="drop-shadow-sm"
              />

              {/* Data Points */}
              {sortedData.map((item, index) => {
                const x = ((index + 0.5) / sortedData.length) * 100;
                const y =
                  100 - ((item.rank - minRank) / (maxRank - minRank || 1)) * 80;

                return (
                  <g key={index}>
                    <circle
                      cx={`${x}%`}
                      cy={`${y}%`}
                      r="4"
                      fill="#3B82F6"
                      stroke="white"
                      strokeWidth="2"
                      className="hover:r-6 transition-all cursor-pointer drop-shadow-sm"
                    />
                    {/* Data Label */}
                    <text
                      x={`${x}%`}
                      y={`${y - 8}%`}
                      textAnchor="middle"
                      className="text-xs font-medium fill-gray-700 pointer-events-none"
                      style={{ fontSize: "10px" }}
                    >
                      {item.rank}
                    </text>
                  </g>
                );
              })}
            </svg>

            {/* Date Labels */}
            <div className="absolute bottom-0 left-0 w-full flex justify-between px-2">
              {sortedData.map((item, index) => (
                <div
                  key={index}
                  className="text-xs text-gray-500 transform -rotate-45 origin-top-left"
                  style={{
                    position: "absolute",
                    left: `${((index + 0.5) / sortedData.length) * 100}%`,
                    transform: "translateX(-50%) rotate(-45deg)",
                    transformOrigin: "center center",
                    bottom: "-35px",
                    fontSize: "10px",
                  }}
                >
                  {item.date}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const DownloadLogsBarChart = ({ data }) => {
    console.log("Bar chart data:", data);

    if (chartLoading) {
      return (
        <div className="space-y-4">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
            <div>
              <h4 className="text-lg font-semibold text-gray-900">
                Daily Downloads Trend
              </h4>
              <p className="text-xs text-gray-500 mt-1">
                Loading chart data...
              </p>
            </div>
            {/* Date Range Filter */}
            <div className="flex flex-col sm:flex-row gap-3 lg:min-w-[400px]">
              <div className="flex-1">
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Date Range (dd-mm-yyyy to dd-mm-yyyy)
                </label>
                <div className="relative">
                  <DatePicker
                    selectsRange={true}
                    startDate={startDate}
                    endDate={endDate}
                    onChange={(update) => {
                      handleDatePickerChange(update);
                    }}
                    dateFormat="dd-MM-yyyy"
                    placeholderText="28-05-2025 to 11-06-2025"
                    className="w-full px-2 py-1.5 pl-8 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    isClearable={true}
                    showPopperArrow={false}
                    popperClassName="z-50"
                    calendarStartDay={1}
                    shouldCloseOnSelect={false}
                    monthsShown={1}
                  />
                  <FiCalendar className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
                </div>
              </div>
              <div className="flex items-end">
                <button
                  onClick={handleSubmitDateRange}
                  disabled={
                    !startDate || !endDate || !selectedPlugin || chartLoading
                  }
                  className="px-4 py-2.5 text-xs bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-md transition-colors whitespace-nowrap font-medium"
                >
                  {chartLoading ? "Loading..." : "Get Data"}
                </button>
              </div>
            </div>
          </div>

          {/* Loading Chart */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div
              className="flex items-center justify-center"
              style={{ height: "280px" }}
            >
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-4">Loading chart data...</p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (!data || data.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No download data available</p>
          <p className="text-xs text-gray-400 mt-2">
            Data length: {data?.length || 0}
          </p>
          <div className="mt-4 p-4 bg-blue-50 rounded">
            <p className="text-sm text-blue-700">
              <strong>To see the bar chart:</strong>
            </p>
            <ol className="text-sm text-blue-600 mt-2 list-decimal list-inside">
              <li>Select a plugin from the dropdown above</li>
              <li>Set a date range and click "Get Data"</li>
              <li>The chart will load with your data</li>
            </ol>
          </div>
        </div>
      );
    }

    // Sort data by date for proper chart display and prepare for chart
    const sortedData = [...data].sort(
      (a, b) => new Date(a.date) - new Date(b.date)
    );

    // Transform data to match dashboard chart format
    const chartData = sortedData.map((item) => ({
      date: new Date(item.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
      downloads: item.downloads,
    }));

    const maxDownloads = Math.max(...chartData.map((d) => d.downloads));
    const minDownloads = Math.min(...chartData.map((d) => d.downloads));
    const avgDownloads = Math.round(
      chartData.reduce((sum, d) => sum + d.downloads, 0) / chartData.length
    );

    return (
      <div className="space-y-4">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
          <div>
            <h4 className="text-lg font-semibold text-gray-900">
              Daily Downloads Trend
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              Download values displayed above each bar
            </p>
            <div className="text-sm text-gray-600 mt-1">
              {chartData.length} days • {minDownloads.toLocaleString()} -{" "}
              {maxDownloads.toLocaleString()} downloads
            </div>
          </div>

          {/* Date Range Filter */}
          <div className="flex flex-col sm:flex-row gap-3 lg:min-w-[400px]">
            <div className="flex-1">
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Date Range (dd-mm-yyyy to dd-mm-yyyy)
              </label>
              <div className="relative">
                <DatePicker
                  selectsRange={true}
                  startDate={startDate}
                  endDate={endDate}
                  onChange={(update) => {
                    handleDatePickerChange(update);
                  }}
                  dateFormat="dd-MM-yyyy"
                  placeholderText="28-05-2025 to 11-06-2025"
                  className="w-full px-2 py-1.5 pl-8 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  isClearable={true}
                  showPopperArrow={false}
                  popperClassName="z-50"
                  calendarStartDay={1}
                  shouldCloseOnSelect={false}
                  monthsShown={1}
                />
                <FiCalendar className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
              </div>
            </div>
            <div className="flex items-end">
              <button
                onClick={handleSubmitDateRange}
                disabled={
                  !startDate || !endDate || !selectedPlugin || chartLoading
                }
                className="px-4 py-2.5 text-xs bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-md transition-colors whitespace-nowrap font-medium"
              >
                {chartLoading ? "Loading..." : "Get Data"}
              </button>
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="bg-blue-50 p-3 rounded-lg text-center">
            <div className="text-lg font-semibold text-blue-600">
              {maxDownloads.toLocaleString()}
            </div>
            <div className="text-xs text-blue-500">Peak Downloads</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="text-lg font-semibold text-green-600">
              {avgDownloads.toLocaleString()}
            </div>
            <div className="text-xs text-green-500">Average Downloads</div>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg text-center">
            <div className="text-lg font-semibold text-gray-600">
              {minDownloads.toLocaleString()}
            </div>
            <div className="text-xs text-gray-500">Minimum Downloads</div>
          </div>
        </div>

        {/* Chart */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="w-full overflow-hidden">
            {chartData.length > 15 ? (
              // For large datasets (>15 days), use horizontal scroll
              <div className="overflow-x-auto pb-2">
                <div
                  className="flex items-end justify-start relative"
                  style={{
                    height: "280px",
                    paddingBottom: "40px",
                    minWidth: `${Math.max(chartData.length * 30, 800)}px`,
                    width: `${Math.max(chartData.length * 30, 800)}px`,
                  }}
                >
                  {chartData.map((item, index) => {
                    const barHeight = (item.downloads / maxDownloads) * 200;
                    const formattedDownloads = item.downloads.toLocaleString();

                    // Dynamic width for scrollable view to use full space
                    const containerWidth = Math.max(chartData.length * 30, 800);
                    const totalSpacing = (chartData.length - 1) * 3; // 3px gaps
                    const availableWidth = containerWidth - totalSpacing;
                    const barWidth = Math.max(
                      18,
                      availableWidth / chartData.length
                    );
                    const spacing = 3;

                    return (
                      <div
                        key={index}
                        className="flex flex-col items-center group relative flex-shrink-0"
                        style={{
                          width: `${barWidth}px`,
                          marginRight:
                            index < chartData.length - 1 ? `${spacing}px` : "0",
                        }}
                      >
                        {/* Data Label (selective for large datasets) */}
                        {index % Math.ceil(chartData.length / 15) === 0 && (
                          <div
                            className="absolute text-xs font-medium text-gray-700 z-20 pointer-events-none"
                            style={{
                              bottom: `${barHeight + 5}px`,
                              transform: "translateX(-50%)",
                              left: "50%",
                              minWidth: "max-content",
                              whiteSpace: "nowrap",
                              fontSize: "9px",
                            }}
                          >
                            {item.downloads > 999
                              ? `${(item.downloads / 1000).toFixed(1)}k`
                              : formattedDownloads}
                          </div>
                        )}

                        <div
                          className="bg-gradient-to-t from-blue-600 to-blue-400 rounded-t-sm transition-all hover:from-blue-700 hover:to-blue-500 cursor-pointer shadow-sm"
                          style={{
                            height: `${barHeight}px`,
                            minHeight: "3px",
                            width: `${barWidth}px`,
                          }}
                          title={`${
                            item.date
                          }: ${item.downloads.toLocaleString()} downloads`}
                        />

                        {/* Date labels - show selectively */}
                        {(index % Math.ceil(chartData.length / 8) === 0 ||
                          index === 0 ||
                          index === chartData.length - 1) && (
                          <div
                            className="absolute text-xs text-gray-500 group-hover:text-gray-700 whitespace-nowrap"
                            style={{
                              bottom: "-35px",
                              transform: "translateX(-50%) rotate(-45deg)",
                              left: "50%",
                              transformOrigin: "center center",
                              fontSize: "8px",
                            }}
                          >
                            {item.date.split(" ")[1] || item.date}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              // For small datasets (≤15 days), fit to container width
              <div
                className="flex items-end relative w-full"
                style={{
                  height: "280px",
                  paddingBottom: "40px",
                  gap: "6px",
                }}
              >
                {chartData.map((item, index) => {
                  const barHeight = (item.downloads / maxDownloads) * 200;
                  const formattedDownloads = item.downloads.toLocaleString();

                  // Equal distribution across full width
                  const barWidthPercent = 100 / chartData.length;

                  return (
                    <div
                      key={index}
                      className="flex flex-col items-center group relative"
                      style={{
                        flex: `1 1 ${barWidthPercent}%`,
                        maxWidth: `${barWidthPercent}%`,
                      }}
                    >
                      {/* Data Label (show all for small datasets) */}
                      <div
                        className="absolute text-xs font-medium text-gray-700 z-20 pointer-events-none"
                        style={{
                          bottom: `${barHeight + 5}px`,
                          transform: "translateX(-50%)",
                          left: "50%",
                          minWidth: "max-content",
                          whiteSpace: "nowrap",
                          fontSize: "10px",
                        }}
                      >
                        {formattedDownloads}
                      </div>

                      <div
                        className="bg-gradient-to-t from-blue-600 to-blue-400 rounded-t-sm transition-all hover:from-blue-700 hover:to-blue-500 cursor-pointer shadow-sm"
                        style={{
                          height: `${barHeight}px`,
                          minHeight: "3px",
                          width: "calc(100% - 4px)",
                          minWidth: "8px",
                        }}
                        title={`${
                          item.date
                        }: ${item.downloads.toLocaleString()} downloads`}
                      />

                      {/* Date labels - show all for small datasets */}
                      <div
                        className="absolute text-xs text-gray-500 group-hover:text-gray-700 whitespace-nowrap"
                        style={{
                          bottom: "-35px",
                          transform: "translateX(-50%)",
                          left: "50%",
                          transformOrigin: "center center",
                          fontSize: "10px",
                        }}
                      >
                        {item.date}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const RatingDistributionChart = ({ ratingData }) => {
    if (!ratingData || !ratingData.ratingBreakdown) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No rating data available</p>
          <div className="mt-4 p-4 bg-yellow-50 rounded">
            <p className="text-sm text-yellow-700">
              <strong>Rating data will be loaded when:</strong>
            </p>
            <ul className="text-sm text-yellow-600 mt-2 list-disc list-inside">
              <li>A plugin is selected from the dropdown above</li>
              <li>The plugin has ratings on WordPress.org</li>
            </ul>
          </div>
        </div>
      );
    }

    const chartData = [
      {
        name: "5 Stars",
        value: parseInt(ratingData.ratingBreakdown.five) || 0,
        color: "#10B981",
      },
      {
        name: "4 Stars",
        value: parseInt(ratingData.ratingBreakdown.four) || 0,
        color: "#34D399",
      },
      {
        name: "3 Stars",
        value: parseInt(ratingData.ratingBreakdown.three) || 0,
        color: "#FCD34D",
      },
      {
        name: "2 Stars",
        value: parseInt(ratingData.ratingBreakdown.two) || 0,
        color: "#F97316",
      },
      {
        name: "1 Star",
        value: parseInt(ratingData.ratingBreakdown.one) || 0,
        color: "#EF4444",
      },
    ];

    // Filter out segments with no ratings
    const filteredData = chartData.filter((item) => item.value > 0);

    // If no rating data exists, show a placeholder
    if (filteredData.length === 0) {
      return (
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900">
            Rating Distribution
          </h4>
          <div className="text-center py-8">
            <p className="text-gray-500">
              No ratings available for this plugin
            </p>
            <div className="mt-4 p-4 bg-gray-50 rounded">
              <p className="text-sm text-gray-600">
                This plugin hasn't received any ratings on WordPress.org yet.
              </p>
            </div>
          </div>
        </div>
      );
    }

    const total = filteredData.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;

    return (
      <div className="space-y-4">
        <div className="text-center">
          <h4 className="text-lg font-semibold text-gray-900">
            Rating Distribution
          </h4>
          <p className="text-xs text-gray-500 mt-1">
            {total.toLocaleString()} total • {ratingData.rating}/5.0 avg
          </p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex flex-col items-center gap-6">
            {/* Pie Chart */}
            <div className="relative">
              <svg width="200" height="200" className="transform -rotate-90">
                {filteredData.map((item, index) => {
                  const percentage = (item.value / total) * 100;
                  const angle = (item.value / total) * 360;
                  const startAngle = currentAngle;
                  const endAngle = currentAngle + angle;

                  const x1 = 100 + 80 * Math.cos((startAngle * Math.PI) / 180);
                  const y1 = 100 + 80 * Math.sin((startAngle * Math.PI) / 180);
                  const x2 = 100 + 80 * Math.cos((endAngle * Math.PI) / 180);
                  const y2 = 100 + 80 * Math.sin((endAngle * Math.PI) / 180);

                  const largeArcFlag = angle > 180 ? 1 : 0;

                  const pathData = [
                    `M 100 100`,
                    `L ${x1} ${y1}`,
                    `A 80 80 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                    `Z`,
                  ].join(" ");

                  // Calculate label position
                  const labelAngle = startAngle + angle / 2;
                  const labelRadius = 55;
                  const labelX =
                    100 + labelRadius * Math.cos((labelAngle * Math.PI) / 180);
                  const labelY =
                    100 + labelRadius * Math.sin((labelAngle * Math.PI) / 180);

                  currentAngle += angle;

                  return (
                    <g key={index}>
                      <path
                        d={pathData}
                        fill={item.color}
                        stroke="white"
                        strokeWidth="3"
                        className="hover:opacity-80 transition-opacity cursor-pointer"
                      />
                      {/* Data label on pie slice */}
                      {percentage > 8 && ( // Only show label if segment is large enough
                        <text
                          x={labelX}
                          y={labelY}
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className="text-xs font-bold fill-white"
                          style={{
                            fontSize: "11px",
                            textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
                            transform: "rotate(90deg)",
                            transformOrigin: `${labelX}px ${labelY}px`,
                          }}
                        >
                          {percentage.toFixed(0)}%
                        </text>
                      )}
                    </g>
                  );
                })}
              </svg>

              {/* Center content */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center bg-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg">
                  <div>
                    <div className="text-sm font-bold text-gray-900">
                      {ratingData.rating}
                    </div>
                    <div className="text-xs text-gray-500">
                      {ratingData.ratingCount > 999
                        ? `${(ratingData.ratingCount / 1000).toFixed(1)}k`
                        : ratingData.ratingCount}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Compact Legend */}
            <div className="space-y-2 w-full">
              <h5 className="font-medium text-gray-900 text-center text-sm">
                Rating Breakdown
              </h5>
              {filteredData.map((item, index) => {
                const percentage = ((item.value / total) * 100).toFixed(1);
                return (
                  <div
                    key={index}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-xs font-medium text-gray-700">
                        {item.name}
                      </span>
                    </div>
                    <div className="text-xs text-gray-600">
                      {item.value > 999
                        ? `${(item.value / 1000).toFixed(1)}k`
                        : item.value}{" "}
                      ({percentage}%)
                    </div>
                  </div>
                );
              })}

              {/* Total ratings */}
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-700">
                    Total
                  </span>
                  <span className="text-xs font-bold text-gray-900">
                    {total > 999
                      ? `${(total / 1000).toFixed(1)}k ratings`
                      : `${total.toLocaleString()} ratings`}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const ReviewStatsPieChart = ({ data }) => {
    if (!data || data.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No review data available</p>
        </div>
      );
    }

    // Get the first plugin's rating breakdown (since we're filtering by single plugin)
    const pluginData = data[0];
    const ratingBreakdown = pluginData.ratingBreakdown || {};

    console.log("Review stats data:", data);
    console.log("Rating breakdown:", ratingBreakdown);

    // Create pie chart data from rating breakdown
    const pieData = [
      { range: "5 Stars", count: parseInt(ratingBreakdown.five) || 0 },
      { range: "4 Stars", count: parseInt(ratingBreakdown.four) || 0 },
      { range: "3 Stars", count: parseInt(ratingBreakdown.three) || 0 },
      { range: "2 Stars", count: parseInt(ratingBreakdown.two) || 0 },
      { range: "1 Star", count: parseInt(ratingBreakdown.one) || 0 },
    ].filter((item) => item.count > 0); // Only show segments with ratings

    const total = pieData.reduce((sum, item) => sum + item.count, 0);
    const colors = [
      "#10B981", // Green for 5.0
      "#34D399", // Light green for 4.0-4.9
      "#FCD34D", // Yellow for 3.0-3.9
      "#F97316", // Orange for 2.0-2.9
      "#EF4444", // Red for 1.0-1.9
      "#9CA3AF", // Gray for No Rating
    ];

    let currentAngle = 0;

    return (
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-gray-900">
          Rating Distribution (Pie Chart)
        </h4>
        <div className="bg-gray-50 rounded-lg p-6">
          <div className="flex flex-col lg:flex-row items-center gap-8">
            {/* Pie Chart */}
            <div className="relative">
              <svg width="240" height="240" className="transform -rotate-90">
                {pieData.map((item, index) => {
                  const percentage = (item.count / total) * 100;
                  const angle = (item.count / total) * 360;
                  const startAngle = currentAngle;
                  const endAngle = currentAngle + angle;

                  const x1 = 120 + 100 * Math.cos((startAngle * Math.PI) / 180);
                  const y1 = 120 + 100 * Math.sin((startAngle * Math.PI) / 180);
                  const x2 = 120 + 100 * Math.cos((endAngle * Math.PI) / 180);
                  const y2 = 120 + 100 * Math.sin((endAngle * Math.PI) / 180);

                  const largeArcFlag = angle > 180 ? 1 : 0;

                  const pathData = [
                    `M 120 120`,
                    `L ${x1} ${y1}`,
                    `A 100 100 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                    `Z`,
                  ].join(" ");

                  currentAngle += angle;

                  // Calculate label position
                  const labelAngle = startAngle + angle / 2;
                  const labelRadius = 70;
                  const labelX =
                    120 + labelRadius * Math.cos((labelAngle * Math.PI) / 180);
                  const labelY =
                    120 + labelRadius * Math.sin((labelAngle * Math.PI) / 180);

                  return (
                    <g key={index}>
                      <path
                        d={pathData}
                        fill={colors[index]}
                        stroke="white"
                        strokeWidth="2"
                        className="hover:opacity-80 transition-opacity cursor-pointer"
                      >
                        <title>{`${item.range}: ${
                          item.count
                        } plugins (${percentage.toFixed(1)}%)`}</title>
                      </path>
                      {/* Data label */}
                      {percentage > 5 && ( // Only show label if segment is large enough
                        <text
                          x={labelX}
                          y={labelY}
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className="text-xs font-medium fill-white"
                          style={{
                            fontSize: "10px",
                            textShadow: "1px 1px 1px rgba(0,0,0,0.5)",
                          }}
                        >
                          {percentage.toFixed(0)}%
                        </text>
                      )}
                    </g>
                  );
                })}
              </svg>

              {/* Center text */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center bg-white rounded-full w-20 h-20 flex items-center justify-center shadow-lg">
                  <div>
                    <div className="text-lg font-bold text-gray-900">
                      {pluginData.latestRating || "0.0"}
                    </div>
                    <div className="text-xs text-gray-500">{total} reviews</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Legend */}
            <div className="space-y-3">
              <h5 className="font-medium text-gray-900">Rating Breakdown</h5>
              {pieData.map((item, index) => {
                const percentage = ((item.count / total) * 100).toFixed(1);
                return (
                  <div
                    key={index}
                    className="flex items-center justify-between min-w-[200px]"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: colors[index] }}
                      />
                      <span className="text-sm font-medium text-gray-700">
                        {item.range}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {item.count} ({percentage}%)
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Plugin Details */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h5 className="font-medium text-gray-900 mb-3">Plugin Details</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {data.map((plugin, index) => (
                <div key={index} className="bg-white rounded-lg p-3 border">
                  <h6 className="font-medium text-gray-900 text-sm mb-2">
                    {getDisplayName(plugin.pluginName)}
                  </h6>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Latest:</span>
                      <span className="font-medium">
                        {plugin.latestRating || "N/A"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Average:</span>
                      <span className="font-medium">
                        {plugin.avgRating?.toFixed(1) || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading && !chartLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600">
            Select a specific plugin to view detailed analytics charts and
            performance trends (data up to yesterday)
          </p>
          {lastRefreshTime && (
            <p className="text-xs text-gray-500 mt-1">
              Last refreshed: {lastRefreshTime.toLocaleString()}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-3">
          {/* Auto-refresh toggle */}
          <div className="flex items-center space-x-2">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="sr-only"
              />
              <div
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoRefresh ? "bg-blue-600" : "bg-gray-200"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    autoRefresh ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </div>
              <span className="ml-2 text-sm text-gray-700">
                Auto-refresh (5min)
              </span>
            </label>
          </div>

          {/* Manual refresh button */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn-secondary flex items-center"
          >
            <FiRefreshCw
              className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
            />
            Refresh Data
          </button>
        </div>
      </div>

      {/* Charts Section */}
      <div className="space-y-6">
        {/* Compact Plugin Filter */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
          <div className="flex flex-col sm:flex-row sm:items-center gap-3">
            <div className="flex items-center gap-2 flex-shrink-0">
              <FiPackage className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Plugin:</span>
            </div>
            <div className="flex-1 max-w-xs">
              <select
                value={selectedPlugin}
                onChange={(e) => handlePluginChange(e.target.value)}
                className="w-full pl-3 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 appearance-none bg-white text-sm"
              >
                <option value="">All Plugins</option>
                {plugins.map((plugin) => (
                  <option key={plugin._id} value={plugin._id}>
                    {getDisplayName(plugin.name)}
                  </option>
                ))}
              </select>
            </div>
            {!selectedPlugin && (
              <span className="text-xs text-gray-500 hidden sm:block">
                Select a plugin to view detailed analytics
              </span>
            )}
          </div>
        </div>

        {/* All Charts Display */}
        {!selectedPlugin ? (
          <div className="card">
            <div className="text-center py-16">
              <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <FiTrendingUp className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Select a Plugin to View Analytics
              </h3>
              <p className="text-gray-600 max-w-md mx-auto">
                Choose a specific plugin from the filter above to view detailed
                download analytics with day-wise horizontal bar chart
                visualization.
              </p>
              <div className="mt-6 flex items-center justify-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <FiDownload className="w-4 h-4 mr-1" />
                  <span>Download Trends</span>
                </div>
                <div className="flex items-center">
                  <FiTrendingUp className="w-4 h-4 mr-1" />
                  <span>Bar Chart</span>
                </div>
                <div className="flex items-center">
                  <FiStar className="w-4 h-4 mr-1" />
                  <span>Interactive Data</span>
                </div>
              </div>
            </div>
          </div>
        ) : chartLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Plugin Rank Change Line Chart - Full Width */}
            <div className="card">
              <RankChangeLineChart data={rankChangeData} />
            </div>

            {/* Daily Downloads (8 columns) and Rating Distribution (4 columns) side by side */}
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              {/* Daily Downloads Trend - 8 columns (2/3 of the width) */}
              <div className="xl:col-span-2">
                <div className="card">
                  <DownloadLogsBarChart
                    data={downloadLogsData}
                    dateRange={dateRange}
                    handleDateRangeChange={handleDateRangeChange}
                    handleDateRangeTextChange={handleDateRangeTextChange}
                    dateRangeText={dateRangeText}
                    handleSubmitDateRange={handleSubmitDateRange}
                    selectedPlugin={selectedPlugin}
                    startDate={startDate}
                    endDate={endDate}
                  />
                </div>
              </div>

              {/* Rating Distribution - 4 columns (1/3 of the width) */}
              <div className="xl:col-span-1">
                <div className="card">
                  <RatingDistributionChart ratingData={ratingData} />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Analytics;
