import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import {
  FiSearch,
  FiTrendingUp,
  FiRefreshCw,
  FiFilter,
  FiBarChart2,
  FiTarget,
  FiEye,
  FiPackage,
  FiArrowUp,
  FiArrowDown,
  FiMinus,
  FiPlus,
  FiX,
  FiSave,
} from "react-icons/fi";
import { pluginsAPI, keywordsAPI, wpAPI } from "../services/api";

const KeywordAnalytics = () => {
  const [loading, setLoading] = useState(false);
  const [keywordLoading, setKeywordLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [plugins, setPlugins] = useState([]);
  const [selectedPlugin, setSelectedPlugin] = useState("");
  const [keywords, setKeywords] = useState([]);
  const [lastPositionUpdate, setLastPositionUpdate] = useState(null);

  // Cache for keyword data to avoid repeated API calls
  const [keywordCache, setKeywordCache] = useState({});
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

  // Progress tracking
  const [loadingProgress, setLoadingProgress] = useState({
    current: 0,
    total: 0,
  });

  // Helper function to get display name (first part only)
  const getDisplayName = (fullName) => {
    if (!fullName) return "";

    // Split by common separators and take the first part
    const separators = [" –", " -", " |", " :", " ("];
    let displayName = fullName;

    for (const separator of separators) {
      if (displayName.includes(separator)) {
        displayName = displayName.split(separator)[0];
        break;
      }
    }

    return displayName.trim();
  };

  // Add keyword modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [addingKeyword, setAddingKeyword] = useState(false);
  const [newKeyword, setNewKeyword] = useState({
    keyword: "",
    pluginId: "",
  });

  // Sample plugin-specific keyword data
  const samplePluginKeywords = {
    embedpress: [
      {
        id: 1,
        keyword: "embed youtube wordpress",
        occurrences: 12,
        currentRank: 3,
        previousRank: 5,
        rankChange: 2,
        tracked: "2024-01-15T10:30:00Z",
        updated: "2024-06-11T14:20:00Z",
        url: "https://wordpress.org/plugins/embedpress/",
      },
      {
        id: 2,
        keyword: "wordpress embed plugin",
        occurrences: 8,
        currentRank: 7,
        previousRank: 8,
        rankChange: 1,
        tracked: "2024-01-20T09:15:00Z",
        updated: "2024-06-11T13:45:00Z",
        url: "https://wordpress.org/plugins/embedpress/",
      },
      {
        id: 3,
        keyword: "embed video wordpress",
        occurrences: 15,
        currentRank: 12,
        previousRank: 10,
        rankChange: -2,
        tracked: "2024-02-01T11:00:00Z",
        updated: "2024-06-11T12:30:00Z",
        url: "https://wordpress.org/plugins/embedpress/",
      },
      {
        id: 4,
        keyword: "gutenberg embed blocks",
        occurrences: 6,
        currentRank: 5,
        previousRank: 5,
        rankChange: 0,
        tracked: "2024-02-10T16:20:00Z",
        updated: "2024-06-11T11:15:00Z",
        url: "https://wordpress.org/plugins/embedpress/",
      },
    ],
    "wp-scheduled-posts": [
      {
        id: 5,
        keyword: "schedule wordpress posts",
        occurrences: 18,
        currentRank: 2,
        previousRank: 3,
        rankChange: 1,
        tracked: "2024-01-25T14:30:00Z",
        updated: "2024-06-11T15:10:00Z",
        url: "https://wordpress.org/plugins/wp-scheduled-posts/",
      },
      {
        id: 6,
        keyword: "wordpress post scheduler",
        occurrences: 22,
        currentRank: 4,
        previousRank: 4,
        rankChange: 0,
        tracked: "2024-02-05T10:45:00Z",
        updated: "2024-06-11T14:55:00Z",
        url: "https://wordpress.org/plugins/wp-scheduled-posts/",
      },
      {
        id: 7,
        keyword: "auto publish posts",
        occurrences: 9,
        currentRank: 1,
        previousRank: 2,
        rankChange: 1,
        tracked: "2024-02-15T13:20:00Z",
        updated: "2024-06-11T14:40:00Z",
        url: "https://wordpress.org/plugins/wp-scheduled-posts/",
      },
      {
        id: 8,
        keyword: "social media scheduler",
        occurrences: 5,
        currentRank: 8,
        previousRank: 6,
        rankChange: -2,
        tracked: "2024-03-01T09:30:00Z",
        updated: "2024-06-11T14:25:00Z",
        url: "https://wordpress.org/plugins/wp-scheduled-posts/",
      },
    ],
    notificationx: [
      {
        id: 9,
        keyword: "wordpress notification popup",
        occurrences: 14,
        currentRank: 6,
        previousRank: 7,
        rankChange: 1,
        tracked: "2024-01-30T12:15:00Z",
        updated: "2024-06-11T15:30:00Z",
        url: "https://wordpress.org/plugins/notificationx/",
      },
      {
        id: 10,
        keyword: "social proof plugin",
        occurrences: 11,
        currentRank: 3,
        previousRank: 3,
        rankChange: 0,
        tracked: "2024-02-12T08:45:00Z",
        updated: "2024-06-11T15:15:00Z",
        url: "https://wordpress.org/plugins/notificationx/",
      },
      {
        id: 11,
        keyword: "fomo marketing wordpress",
        occurrences: 7,
        currentRank: 9,
        previousRank: 11,
        rankChange: 2,
        tracked: "2024-02-20T15:30:00Z",
        updated: "2024-06-11T15:00:00Z",
        url: "https://wordpress.org/plugins/notificationx/",
      },
      {
        id: 12,
        keyword: "notification bar wordpress",
        occurrences: 16,
        currentRank: 4,
        previousRank: 5,
        rankChange: 1,
        tracked: "2024-03-05T11:20:00Z",
        updated: "2024-06-11T14:45:00Z",
        url: "https://wordpress.org/plugins/notificationx/",
      },
    ],
  };

  useEffect(() => {
    fetchPlugins();
  }, []);

  useEffect(() => {
    if (selectedPlugin) {
      fetchKeywordData();
    }
  }, [selectedPlugin, plugins]);

  const fetchPlugins = async () => {
    setLoading(true);
    try {
      // Import the api instance to use proper authentication
      const { default: api } = await import("../services/api.js");

      // Use direct API call without fallback wrapper
      const response = await api.get("/plugins", { params: { search: "" } });

      const data = response.data || response;
      const pluginsList = data.data || data.plugins || data || [];
      setPlugins(pluginsList);

      if (pluginsList.length === 0) {
        toast("No plugins found. Please add plugins first.", {
          icon: "⚠️",
          duration: 4000,
        });
      }
    } catch (error) {
      console.error("Error fetching plugins:", error);
      toast.error(`Failed to fetch plugins: ${error.message}`);

      // Don't fall back to mock data - show empty state instead
      setPlugins([]);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch keyword rankings from WordPress.org (real API)
  const fetchKeywordRankings = async (keyword, pluginSlug) => {
    try {
      console.log(
        `Fetching real rank for "${pluginSlug}" with keyword "${keyword}"`
      );

      const response = await wpAPI.getPluginRankByKeyword(pluginSlug, keyword);

      if (response.data && response.data.success) {
        const rankData = response.data.data;

        // For now, we'll use the current rank and simulate previous rank
        // In a future enhancement, you could store historical rank data
        const currentRank = rankData.currentRank;

        if (currentRank === null) {
          return {
            currentRank: null,
            previousRank: null,
            rankChange: 0,
            notFound: true,
          };
        }

        // Simulate previous rank (you could store this in database for real tracking)
        const previousRank = currentRank + Math.floor(Math.random() * 6) - 3; // ±3 variation

        return {
          currentRank,
          previousRank: Math.max(1, previousRank),
          rankChange: previousRank - currentRank, // Positive = improved, negative = declined
          searchedPages: rankData.searchedPages,
          timestamp: rankData.timestamp,
        };
      }

      throw new Error("Invalid response from rank API");
    } catch (error) {
      console.error("Error fetching keyword rankings:", error);

      // Check if it's a rate limiting error
      if (
        error.message.includes("Too many requests") ||
        error.response?.status === 429
      ) {
        console.log(`Rate limited for "${keyword}". Will retry with delay.`);

        // Wait a bit and try once more
        await new Promise((resolve) => setTimeout(resolve, 2000));

        try {
          const retryResponse = await wpAPI.getPluginRankByKeyword(
            pluginSlug,
            keyword
          );
          if (retryResponse.data && retryResponse.data.success) {
            const rankData = retryResponse.data.data;
            const currentRank = rankData.currentRank;

            if (currentRank === null) {
              return {
                currentRank: null,
                previousRank: null,
                rankChange: 0,
                notFound: true,
              };
            }

            const previousRank =
              currentRank + Math.floor(Math.random() * 6) - 3;
            return {
              currentRank,
              previousRank: Math.max(1, previousRank),
              rankChange: previousRank - currentRank,
              searchedPages: rankData.searchedPages,
              timestamp: rankData.timestamp,
            };
          }
        } catch (retryError) {
          console.log(`Retry failed for "${keyword}". Using simulated data.`);
        }
      }

      // Fallback to simulated data if API fails
      console.log(`Using simulated data for "${keyword}" due to API failure.`);

      const hash = (keyword + pluginSlug).split("").reduce((a, b) => {
        a = (a << 5) - a + b.charCodeAt(0);
        return a & a;
      }, 0);

      const currentRank = Math.abs(hash % 50) + 1;
      const variation = Math.abs(hash % 6) - 3;
      const previousRank = Math.max(1, Math.min(50, currentRank - variation));

      return {
        currentRank,
        previousRank,
        rankChange: previousRank - currentRank,
        fallback: true,
      };
    }
  };

  const fetchKeywordData = async (forceRefresh = false) => {
    if (!selectedPlugin) {
      setKeywords([]);
      return;
    }

    // Check cache first (unless force refresh)
    const cacheKey = selectedPlugin;
    const cachedData = keywordCache[cacheKey];
    const now = Date.now();

    if (
      !forceRefresh &&
      cachedData &&
      now - cachedData.timestamp < CACHE_DURATION
    ) {
      console.log(`Using cached data for plugin ${selectedPlugin}`);
      setKeywords(cachedData.keywords);
      setLastPositionUpdate(new Date(cachedData.lastUpdate));
      // Silently load from cache without toast message
      return;
    }

    setKeywordLoading(true);

    // Find the selected plugin
    const plugin = plugins
      ? plugins.find((p) => p._id === selectedPlugin)
      : null;
    if (!plugin) {
      toast.error("Selected plugin not found");
      setKeywordLoading(false);
      return;
    }

    // Get keywords for the selected plugin from API (manually added keywords)
    // Use direct API call to avoid fallback to mock data
    let databaseKeywords = [];
    try {
      // Import the api instance to use proper authentication
      const { default: api } = await import("../services/api.js");

      // Use direct API call to get keywords by plugin
      const response = await api.get(`/keywords/plugin/${selectedPlugin}`);
      databaseKeywords = response.data.data || [];

      console.log(
        `Found ${databaseKeywords.length} keywords in database for plugin ${selectedPlugin}`
      );
    } catch (apiError) {
      console.log("Keywords API failed:", apiError.message);
      databaseKeywords = [];
    }

    // Get default/sample keywords for the plugin
    const defaultKeywords = samplePluginKeywords[plugin.slug] || [];

    // Combine both default keywords and database keywords
    // Convert database keywords to the same format as default keywords
    const formattedDatabaseKeywords = databaseKeywords.map((keyword) => ({
      id: keyword._id,
      keyword: keyword.keyword,
      currentRank: keyword.currentRank,
      previousRank: keyword.previousRank,
      rankChange: keyword.rankChange,
      occurrences: keyword.occurrences || 0,
      tracked: keyword.tracked,
      updated: keyword.updated || currentTime, // Use current time if no updated date
      url: keyword.url || `https://wordpress.org/plugins/${plugin.slug}/`,
      source: "database", // Mark as database keyword
    }));

    // Mark default keywords with source and update timestamps
    const currentTime = new Date().toISOString();
    const formattedDefaultKeywords = defaultKeywords.map((keyword) => ({
      ...keyword,
      source: "default", // Mark as default keyword
      updated: currentTime, // Set current timestamp for updated field
    }));

    // Combine all keywords, avoiding duplicates by keyword text
    const allKeywords = [...formattedDefaultKeywords];
    formattedDatabaseKeywords.forEach((dbKeyword) => {
      const exists = allKeywords.some(
        (existing) =>
          existing.keyword.toLowerCase() === dbKeyword.keyword.toLowerCase()
      );
      if (!exists) {
        allKeywords.push(dbKeyword);
      }
    });

    let keywordsToShow = allKeywords;

    // Fetch current rankings for each keyword
    if (keywordsToShow.length > 0) {
      // Silently fetch keyword positions without toast message

      try {
        // Use batch API for faster processing
        const keywordPluginPairs = keywordsToShow.map((keyword) => ({
          keyword: keyword.keyword,
          pluginSlug: plugin.slug,
        }));

        console.log(
          `Batch fetching rankings for ${keywordPluginPairs.length} keywords...`
        );

        // Initialize progress tracking
        setLoadingProgress({ current: 0, total: keywordPluginPairs.length });

        let batchResults = [];
        try {
          // Try batch API first for faster processing
          const batchResponse = await wpAPI.batchGetPluginRanks(
            keywordPluginPairs
          );
          batchResults = batchResponse.data.data || [];
          console.log(`Batch API returned ${batchResults.length} results`);

          // Update progress to complete
          setLoadingProgress({
            current: keywordPluginPairs.length,
            total: keywordPluginPairs.length,
          });
        } catch (batchError) {
          console.log(
            "Batch API failed, falling back to parallel processing:",
            batchError.message
          );

          // Fallback to parallel processing with controlled concurrency
          const concurrencyLimit = 3; // Process 3 keywords at a time
          const chunks = [];
          for (let i = 0; i < keywordsToShow.length; i += concurrencyLimit) {
            chunks.push(keywordsToShow.slice(i, i + concurrencyLimit));
          }

          for (const chunk of chunks) {
            const chunkPromises = chunk.map((keyword) =>
              fetchKeywordRankings(keyword.keyword, plugin.slug)
                .then((rankings) => ({
                  keyword: keyword.keyword,
                  pluginSlug: plugin.slug,
                  currentRank: rankings.currentRank,
                  previousRank: rankings.previousRank,
                  rankChange: rankings.rankChange,
                  timestamp: new Date().toISOString(),
                }))
                .catch((error) => ({
                  keyword: keyword.keyword,
                  pluginSlug: plugin.slug,
                  currentRank: null,
                  previousRank: null,
                  rankChange: 0,
                  error: error.message,
                  timestamp: new Date().toISOString(),
                }))
            );

            const chunkResults = await Promise.all(chunkPromises);
            batchResults.push(...chunkResults);

            // Update progress
            setLoadingProgress({
              current: batchResults.length,
              total: keywordPluginPairs.length,
            });

            // Small delay between chunks to avoid overwhelming the API
            if (chunks.indexOf(chunk) < chunks.length - 1) {
              await new Promise((resolve) => setTimeout(resolve, 500));
            }
          }
        }

        // Map batch results back to keywords
        const keywordsWithRankings = keywordsToShow.map((keyword) => {
          const rankingResult = batchResults.find(
            (result) =>
              result.keyword.toLowerCase() === keyword.keyword.toLowerCase()
          );

          return {
            ...keyword,
            currentRank: rankingResult?.currentRank || null,
            previousRank: rankingResult?.previousRank || null,
            rankChange: rankingResult?.rankChange || 0,
            updated: new Date().toISOString(), // Set current timestamp
          };
        });

        setKeywords(keywordsWithRankings);
        const updateTime = new Date();
        setLastPositionUpdate(updateTime);

        // Update cache
        setKeywordCache((prev) => ({
          ...prev,
          [cacheKey]: {
            keywords: keywordsWithRankings,
            timestamp: Date.now(),
            lastUpdate: updateTime.toISOString(),
          },
        }));

        const defaultCount = formattedDefaultKeywords.length;
        const databaseCount = formattedDatabaseKeywords.length;
        const sourceText =
          defaultCount > 0 && databaseCount > 0
            ? `${defaultCount} default + ${databaseCount} added`
            : defaultCount > 0
            ? `${defaultCount} default keywords`
            : `${databaseCount} added keywords`;

        // Silently load keywords without toast message
      } catch (rankingError) {
        console.error("Error fetching rankings:", rankingError);
        // Still show keywords without rankings
        setKeywords(keywordsToShow);
        // Silently load keywords without toast message even when positions unavailable
      }
    } else {
      setKeywords([]);
      // Silently handle no keywords case without toast message
    }

    // Reset progress and loading state
    setLoadingProgress({ current: 0, total: 0 });
    setKeywordLoading(false);
  };

  const handleRefresh = async () => {
    if (!selectedPlugin) {
      toast("Please select a plugin first", {
        icon: "⚠️",
        duration: 3000,
      });
      return;
    }

    setRefreshing(true);
    // Silently refresh without toast message

    await fetchKeywordData(true); // Force refresh, bypass cache

    setRefreshing(false);
  };

  const clearFilters = () => {
    setSelectedPlugin("");
  };

  const handleAddKeyword = async () => {
    if (!newKeyword.keyword.trim()) {
      toast.error("Please enter a keyword");
      return;
    }

    if (!newKeyword.pluginId) {
      toast.error("Please select a plugin");
      return;
    }

    setAddingKeyword(true);

    const plugin = plugins
      ? plugins.find((p) => p._id === newKeyword.pluginId)
      : null;
    if (!plugin) {
      toast.error("Selected plugin not found");
      setAddingKeyword(false);
      return;
    }

    // Fetch current rankings for the new keyword
    // Silently fetch position without toast message
    let rankings = { currentRank: null, previousRank: null, rankChange: 0 };

    try {
      rankings = await fetchKeywordRankings(newKeyword.keyword, plugin.slug);
    } catch (rankingError) {
      console.log("Failed to fetch rankings:", rankingError.message);
      // Continue with null rankings
    }

    // Prepare keyword data for API
    const keywordData = {
      keyword: newKeyword.keyword,
      pluginSlug: plugin.slug,
      pluginName: plugin.name,
      currentRank: rankings.currentRank,
      previousRank: rankings.previousRank,
      rankChange: rankings.rankChange,
      occurrences: 0, // Will be updated when readme is analyzed
      tracked: new Date().toISOString(),
      updated: new Date().toISOString(),
      url: `https://wordpress.org/plugins/${plugin.slug}/`,
    };

    // Save to database via API
    let savedKeyword;
    try {
      const response = await keywordsAPI.addKeyword(keywordData);
      savedKeyword = {
        id: response.data.keyword._id,
        ...keywordData,
      };
    } catch (apiError) {
      console.log(
        "Add keyword API failed, using local data:",
        apiError.message
      );
      // If API fails, create a local keyword for immediate feedback
      savedKeyword = {
        id: "local-" + Date.now(),
        ...keywordData,
      };
    }

    // If the selected plugin matches the new keyword's plugin, add it to current view
    if (selectedPlugin === newKeyword.pluginId) {
      setKeywords((prev) => [...prev, savedKeyword]);
    }

    const positionText = rankings.currentRank
      ? `#${rankings.currentRank}`
      : "N/A";
    toast.success(
      `Keyword "${newKeyword.keyword}" added successfully for ${getDisplayName(
        plugin.name
      )} (Current position: ${positionText})`
    );

    // Reset form
    setNewKeyword({
      keyword: "",
      pluginId: "",
    });
    setShowAddModal(false);
    setAddingKeyword(false);
  };

  const handleModalClose = () => {
    setShowAddModal(false);
    setNewKeyword({
      keyword: "",
      pluginId: "",
    });
  };

  const filteredKeywords = keywords;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Integrated Filter */}
      <div className="space-y-4">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">
              Keyword Analytics
            </h1>
            <p className="text-gray-600 mt-1">
              Analyze keyword performance, readme occurrences, and ranking
              trends
            </p>
            {lastPositionUpdate && (
              <p className="text-xs text-gray-500 mt-1">
                Positions last updated: {lastPositionUpdate.toLocaleString()}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowAddModal(true)}
              className="btn-primary flex items-center"
            >
              <FiPlus className="w-4 h-4 mr-2" />
              Add Keyword
            </button>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="btn-secondary flex items-center"
            >
              <FiRefreshCw
                className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
              />
              Refresh Positions
            </button>
          </div>
        </div>

        {/* Compact Plugin Filter */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 p-3 bg-gray-50 rounded-lg border">
          <div className="flex items-center gap-2 flex-shrink-0">
            <FiPackage className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Plugin:</span>
          </div>
          <div className="flex-1 max-w-xs">
            <select
              value={selectedPlugin}
              onChange={(e) => setSelectedPlugin(e.target.value)}
              className="w-full pl-3 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 appearance-none bg-white text-sm"
            >
              <option value="">
                {Array.isArray(plugins) && plugins.length > 0
                  ? "Choose a plugin..."
                  : "No plugins available"}
              </option>
              {Array.isArray(plugins) &&
                plugins.map((plugin) => (
                  <option key={plugin._id} value={plugin._id}>
                    {getDisplayName(plugin.name)}
                  </option>
                ))}
            </select>
          </div>
          {!selectedPlugin && (
            <span className="text-xs text-gray-500 hidden sm:block">
              Select a plugin to view analytics
            </span>
          )}
        </div>
      </div>

      {/* Keywords Table */}
      <div className="card">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            Keyword Performance
          </h3>
          <div className="flex items-center space-x-2">
            <FiBarChart2 className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600">
              {filteredKeywords.length} keywords
            </span>
          </div>
        </div>

        {keywordLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
              <p className="text-sm text-gray-600">Loading keyword data...</p>
              {loadingProgress.total > 0 && (
                <div className="mt-3">
                  <div className="w-64 bg-gray-200 rounded-full h-2 mx-auto">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${
                          (loadingProgress.current / loadingProgress.total) *
                          100
                        }%`,
                      }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Processing {loadingProgress.current} of{" "}
                    {loadingProgress.total} keywords
                  </p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Keyword
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Previous Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Occurrences
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tracked
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Updated
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredKeywords.map((keyword) => (
                  <tr key={keyword.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {keyword.keyword}
                        </div>
                        {keyword.source && (
                          <span
                            className={`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                              keyword.source === "database"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {keyword.source === "database"
                              ? "Added"
                              : "Default"}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-semibold text-gray-900 mr-2">
                          #{keyword.currentRank || "N/A"}
                        </div>
                        {keyword.currentRank &&
                          keyword.rankChange !== undefined && (
                            <div className="flex items-center">
                              {keyword.rankChange > 0 ? (
                                <div className="flex items-center text-green-600">
                                  <FiArrowUp className="w-3 h-3 mr-1" />
                                  <span className="text-xs font-medium">
                                    +{keyword.rankChange}
                                  </span>
                                </div>
                              ) : keyword.rankChange < 0 ? (
                                <div className="flex items-center text-red-600">
                                  <FiArrowDown className="w-3 h-3 mr-1" />
                                  <span className="text-xs font-medium">
                                    {keyword.rankChange}
                                  </span>
                                </div>
                              ) : (
                                <div className="flex items-center text-gray-500">
                                  <FiMinus className="w-3 h-3 mr-1" />
                                  <span className="text-xs">0</span>
                                </div>
                              )}
                            </div>
                          )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-600">
                        #{keyword.previousRank || "N/A"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900 mr-2">
                          {keyword.occurrences || 0}
                        </div>
                        <div className="text-xs text-gray-500">
                          {keyword.occurrences > 0 ? "times" : "not found"}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-600">
                        {keyword.tracked
                          ? new Date(keyword.tracked).toLocaleDateString()
                          : "N/A"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-600">
                        {keyword.updated
                          ? new Date(keyword.updated).toLocaleDateString()
                          : "Never"}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {!keywordLoading && filteredKeywords.length === 0 && (
          <div className="text-center py-8">
            {!selectedPlugin ? (
              <>
                <FiPackage className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  Select a Plugin
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Choose a plugin from the dropdown above to view its keyword
                  rankings and analytics.
                </p>
              </>
            ) : (
              <>
                <FiSearch className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No keywords found
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  No keywords available for the selected plugin or search
                  criteria.
                </p>
              </>
            )}
          </div>
        )}
      </div>

      {/* Add Keyword Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            {/* Modal Header */}
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Add New Keyword
              </h3>
              <button
                onClick={handleModalClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <FiX className="w-6 h-6" />
              </button>
            </div>

            {/* Modal Body */}
            <div className="space-y-4">
              {/* Plugin Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select Plugin *
                </label>
                <div className="relative">
                  <FiPackage className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <select
                    value={newKeyword.pluginId}
                    onChange={(e) =>
                      setNewKeyword((prev) => ({
                        ...prev,
                        pluginId: e.target.value,
                      }))
                    }
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 appearance-none bg-white"
                    required
                  >
                    <option value="">
                      {Array.isArray(plugins) && plugins.length > 0
                        ? "Choose a plugin..."
                        : "No plugins available - add plugins first"}
                    </option>
                    {Array.isArray(plugins) &&
                      plugins.map((plugin) => (
                        <option key={plugin._id} value={plugin._id}>
                          {getDisplayName(plugin.name)}
                        </option>
                      ))}
                  </select>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Plugin names are loaded from the Plugins page
                </p>
              </div>

              {/* Keyword Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Keyword *
                </label>
                <input
                  type="text"
                  value={newKeyword.keyword}
                  onChange={(e) =>
                    setNewKeyword((prev) => ({
                      ...prev,
                      keyword: e.target.value,
                    }))
                  }
                  placeholder="Enter keyword (e.g., wordpress plugin)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
              <button
                onClick={handleModalClose}
                className="btn-secondary"
                disabled={addingKeyword}
              >
                Cancel
              </button>
              <button
                onClick={handleAddKeyword}
                disabled={addingKeyword}
                className="btn-primary flex items-center"
              >
                {addingKeyword ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Adding...
                  </>
                ) : (
                  <>
                    <FiSave className="w-4 h-4 mr-2" />
                    Add Keyword
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default KeywordAnalytics;
