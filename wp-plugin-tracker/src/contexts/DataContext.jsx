import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { pluginsAPI, wpAPI, dataSyncAPI } from "../services/api";
import toast from "react-hot-toast";

const DataContext = createContext();

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error("useData must be used within a DataProvider");
  }
  return context;
};

export const DataProvider = ({ children }) => {
  // Central state for all data
  const [plugins, setPlugins] = useState([]);
  const [pluginStats, setPluginStats] = useState(null);
  const [downloadData, setDownloadData] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Cache management
  const [cache, setCache] = useState({
    plugins: null,
    downloadStats: {},
    lastFetch: null,
  });

  // Cache duration (5 minutes)
  const CACHE_DURATION = 5 * 60 * 1000;

  // Check if cache is valid
  const isCacheValid = useCallback((timestamp) => {
    if (!timestamp) return false;
    return Date.now() - timestamp < CACHE_DURATION;
  }, []);

  // Fetch all plugins data
  const fetchPluginsData = useCallback(
    async (forceRefresh = false) => {
      try {
        // Check cache first using current state
        if (!forceRefresh) {
          const currentCache = cache;
          if (currentCache.plugins && isCacheValid(currentCache.lastFetch)) {
            setPlugins(currentCache.plugins);
            setLoading(false);
            return currentCache.plugins;
          }
        }

        setLoading(true);
        const response = await pluginsAPI.getDashboardData();
        const pluginsData = response.data.plugins;

        // Update state and cache
        setPlugins(pluginsData);
        setCache((prev) => ({
          ...prev,
          plugins: pluginsData,
          lastFetch: Date.now(),
        }));
        setLastUpdated(new Date());

        return pluginsData;
      } catch (error) {
        console.error("Error fetching plugins data:", error);

        // Handle different types of errors
        if (error.message?.includes("Network Error")) {
          toast.error(
            "Unable to connect to server. Please check if the backend is running."
          );
        } else if (error.message?.includes("Authentication required")) {
          // Don't show error for auth issues on public endpoints
          console.log(
            "Authentication error on public endpoint - this should not happen"
          );
        } else {
          toast.error("Failed to fetch plugins data");
        }

        // Don't throw error to prevent app crash, just log it
        console.error("DataContext error details:", error);
      } finally {
        setLoading(false);
      }
    },
    [isCacheValid] // Removed cache dependencies to prevent infinite loop
  );

  // Fetch plugin statistics
  const fetchPluginStats = useCallback(async (forceRefresh = false) => {
    try {
      const response = await pluginsAPI.getPluginStats();
      setPluginStats(response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching plugin stats:", error);
      throw error;
    }
  }, []);

  // Fetch download statistics for a specific plugin
  const fetchPluginDownloadStats = useCallback(
    async (slug, forceRefresh = false) => {
      try {
        // Check cache first using current state
        if (!forceRefresh) {
          const currentCache = cache;
          if (
            currentCache.downloadStats[slug] &&
            isCacheValid(currentCache.downloadStats[slug].timestamp)
          ) {
            return currentCache.downloadStats[slug].data;
          }
        }

        const response = await wpAPI.getPluginDownloadStats(slug);
        const downloadStats = response.data.data;

        // Update cache
        setCache((prev) => ({
          ...prev,
          downloadStats: {
            ...prev.downloadStats,
            [slug]: {
              data: downloadStats,
              timestamp: Date.now(),
            },
          },
        }));

        return downloadStats;
      } catch (error) {
        console.error(`Error fetching download stats for ${slug}:`, error);
        throw error;
      }
    },
    [isCacheValid] // Removed cache dependencies to prevent infinite loop
  );

  // Fetch all download data for all plugins
  const fetchAllDownloadData = useCallback(
    async (forceRefresh = false) => {
      try {
        const currentPlugins =
          plugins.length > 0 ? plugins : await fetchPluginsData();

        const downloadPromises = currentPlugins.map(async (plugin) => {
          try {
            const stats = await fetchPluginDownloadStats(
              plugin.slug,
              forceRefresh
            );
            return { slug: plugin.slug, stats };
          } catch (error) {
            console.error(`Error fetching stats for ${plugin.slug}:`, error);
            return { slug: plugin.slug, stats: null };
          }
        });

        const allStats = await Promise.all(downloadPromises);
        const statsMap = {};
        allStats.forEach(({ slug, stats }) => {
          statsMap[slug] = stats;
        });

        setDownloadData(statsMap);
        return statsMap;
      } catch (error) {
        console.error("Error fetching all download data:", error);
        throw error;
      }
    },
    [plugins, fetchPluginsData, fetchPluginDownloadStats]
  );

  // Refresh all data from WordPress.org and store in database
  const refreshAllData = useCallback(async () => {
    setRefreshing(true);
    try {
      // Use the new centralized sync API
      const syncResponse = await dataSyncAPI.syncAll(true);
      console.log("Sync started:", syncResponse.data);

      // Wait a moment for sync to process, then fetch fresh data
      setTimeout(async () => {
        try {
          const [pluginsData, statsData, downloadStats] = await Promise.all([
            fetchPluginsData(true),
            fetchPluginStats(true),
            fetchAllDownloadData(true),
          ]);

          setLastUpdated(new Date());
          toast.success("All data refreshed successfully!");

          return { pluginsData, statsData, downloadStats };
        } catch (error) {
          console.error("Error fetching refreshed data:", error);
          toast.error("Data sync started but failed to fetch updated data");
        }
      }, 2000);
    } catch (error) {
      console.error("Error refreshing all data:", error);
      toast.error("Failed to refresh data");
      throw error;
    } finally {
      setRefreshing(false);
    }
  }, [fetchPluginsData, fetchPluginStats, fetchAllDownloadData]);

  // Get plugin by slug
  const getPluginBySlug = useCallback(
    (slug) => {
      return plugins.find((plugin) => plugin.slug === slug);
    },
    [plugins]
  );

  // Get download stats for a plugin
  const getPluginDownloadStats = useCallback(
    (slug) => {
      return downloadData[slug] || null;
    },
    [downloadData]
  );

  // Update a specific plugin in the state
  const updatePlugin = useCallback((updatedPlugin) => {
    setPlugins((prev) =>
      prev.map((plugin) =>
        plugin._id === updatedPlugin._id ? updatedPlugin : plugin
      )
    );

    // Update cache
    setCache((prev) => ({
      ...prev,
      plugins: prev.plugins?.map((plugin) =>
        plugin._id === updatedPlugin._id ? updatedPlugin : plugin
      ),
    }));
  }, []);

  // Add new plugin to state
  const addPlugin = useCallback((newPlugin) => {
    setPlugins((prev) => [newPlugin, ...prev]);

    // Update cache
    setCache((prev) => ({
      ...prev,
      plugins: prev.plugins ? [newPlugin, ...prev.plugins] : [newPlugin],
    }));
  }, []);

  // Remove plugin from state
  const removePlugin = useCallback((pluginId) => {
    setPlugins((prev) => prev.filter((plugin) => plugin._id !== pluginId));

    // Update cache
    setCache((prev) => ({
      ...prev,
      plugins: prev.plugins?.filter((plugin) => plugin._id !== pluginId),
    }));
  }, []);

  // Initialize data on mount (retry logic disabled to prevent infinite loops)
  useEffect(() => {
    fetchPluginsData().catch((error) => {
      console.error("Failed to initialize data:", error);
    });
  }, [fetchPluginsData]);

  const value = {
    // State
    plugins,
    pluginStats,
    downloadData,
    loading,
    refreshing,
    lastUpdated,

    // Actions
    fetchPluginsData,
    fetchPluginStats,
    fetchPluginDownloadStats,
    fetchAllDownloadData,
    refreshAllData,

    // Getters
    getPluginBySlug,
    getPluginDownloadStats,

    // Mutations
    updatePlugin,
    addPlugin,
    removePlugin,

    // Cache info
    isCacheValid: isCacheValid(cache.lastFetch),
    cacheAge: cache.lastFetch ? Date.now() - cache.lastFetch : null,
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
};
