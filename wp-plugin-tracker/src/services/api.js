import axios from "axios";
import Cookies from "js-cookie";
import mockAPI from "./mockAPI.js";
import toast from "react-hot-toast";

const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:5003/api";

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      Cookies.remove("token");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// Note: Mock API fallback has been disabled to ensure real data is always used

// Wrapper function to use real API only (mock fallback disabled)
const apiCall = async (apiFunction, mockFunction) => {
  try {
    // Always try the real API first
    const result = await apiFunction();
    return result;
  } catch (error) {
    console.error("API call failed:", error.response?.status || error.message);

    // For development: show the error instead of falling back to mock data
    if (error.response?.status === 401) {
      throw new Error("Authentication required. Please log in again.");
    } else if (error.response?.status === 404) {
      throw new Error("API endpoint not found.");
    } else {
      throw new Error(`API call failed: ${error.message}`);
    }
  }
};

// Auth API
export const authAPI = {
  login: (credentials) =>
    apiCall(
      () => api.post("/auth/login", credentials),
      () => mockAPI.auth.login(credentials)
    ),
  verifyToken: () =>
    apiCall(
      () => api.get("/auth/verify"),
      () => mockAPI.auth.verifyToken()
    ),
  changePassword: (data) =>
    apiCall(
      () => api.put("/auth/change-password", data),
      () => mockAPI.auth.changePassword(data)
    ),
};

// Users API
export const usersAPI = {
  getUsers: (params) => api.get("/users", { params }),
  createUser: (userData) => api.post("/users", userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  getUserById: (id) => api.get(`/users/${id}`),
  updateProfile: (userData) =>
    apiCall(
      () => api.put("/users/profile", userData),
      () => mockAPI.users.updateProfile(userData)
    ),
};

// Plugins API
export const pluginsAPI = {
  getPlugins: (params) =>
    apiCall(
      () => api.get("/plugins", { params }),
      () => mockAPI.wp.getPlugins(params)
    ),
  addPlugin: (pluginData) =>
    apiCall(
      () => api.post("/plugins", pluginData),
      () => mockAPI.wp.addPlugin(pluginData)
    ),
  updatePlugin: (id, pluginData) =>
    apiCall(
      () => api.put(`/plugins/${id}`, pluginData),
      () => mockAPI.wp.updatePlugin(id, pluginData)
    ),
  deletePlugin: (id) => api.delete(`/plugins/${id}`),
  getPluginBySlug: (slug) =>
    apiCall(
      () => api.get(`/plugins/slug/${slug}`),
      () => mockAPI.wp.getPluginBySlug(slug)
    ),
  refreshPluginData: (id) =>
    apiCall(
      () => api.post(`/plugins/${id}/refresh`),
      () => mockAPI.wp.refreshPluginData(id)
    ),
  getPluginStats: () =>
    apiCall(
      () => api.get("/plugins/stats"),
      () => mockAPI.wp.getPluginStats()
    ),
  getDashboardData: () =>
    apiCall(
      () => api.get("/plugins/dashboard"),
      () => mockAPI.wp.getDashboardData()
    ),
};

// WordPress API (for fetching plugin data)
export const wpAPI = {
  getPluginInfo: (slug) => api.get(`/wp/plugin/${slug}`),
  getPluginSupport: (slug) => api.get(`/wp/plugin/${slug}/support`),
  getPluginDownloadStats: (slug) =>
    apiCall(
      () => api.get(`/wp/plugin/${slug}/downloads`),
      () => mockAPI.wp.getPluginDownloadStats(slug)
    ),
  storeDownloadData: () =>
    apiCall(
      () => api.post(`/wp/store-download-data`),
      () => mockAPI.wp.storeDownloadData()
    ),
  getAnalytics: (params) => api.get(`/wp/analytics`, { params }),
  // New endpoints for plugin-wise download data
  fetchPluginData: (slug) => api.post(`/wp/fetch-plugin-data/${slug}`),
  getDownloadData: (params) =>
    apiCall(
      () => api.get(`/wp/download-data`, { params }),
      () => mockAPI.wp.getDownloadData(params)
    ),
  getDownloadSummary: () => api.get(`/wp/download-summary`),
  // Keyword ranking endpoints
  getPluginRankByKeyword: (slug, keyword) =>
    api.get(`/wp/plugin-rank/${slug}/${encodeURIComponent(keyword)}`),
  batchGetPluginRanks: (keywordPluginPairs) =>
    api.post(`/wp/batch-plugin-ranks`, { keywordPluginPairs }),
  // Readme analysis endpoint
  analyzePluginReadme: (slug, keyword) =>
    api.get(`/wp/analyze-readme/${slug}/${encodeURIComponent(keyword)}`),
};

// Data Sync API
export const dataSyncAPI = {
  getStatus: () => api.get("/data-sync/status"),
  syncAll: (forceRefresh = false) =>
    api.post("/data-sync/sync-all", { forceRefresh }),
  syncPlugin: (slug) => api.post(`/data-sync/sync-plugin/${slug}`),
  getPlugins: (params) => api.get("/data-sync/plugins", { params }),
  getDownloadStats: (params) =>
    api.get("/data-sync/download-stats", { params }),
  startAutoSync: (intervalMinutes = 60) =>
    api.post("/data-sync/auto-sync/start", { intervalMinutes }),
  stopAutoSync: () => api.post("/data-sync/auto-sync/stop"),
};

// Keywords API
export const keywordsAPI = {
  // New direct API calls (for real backend)
  getAll: (params = {}) => api.get("/keywords", { params }),
  getById: (id) => api.get(`/keywords/${id}`),
  create: (data) => api.post("/keywords", data),
  update: (id, data) => api.put(`/keywords/${id}`, data),
  delete: (id) => api.delete(`/keywords/${id}`),
  refresh: (id) => api.post(`/keywords/${id}/refresh`),
  batchRefresh: (data = {}) => api.post("/keywords/batch-refresh", data),

  // Legacy API calls with fallback (for compatibility)
  getKeywords: (params) =>
    apiCall(
      () => api.get("/keywords", { params }),
      () => mockAPI.keywords.getKeywords(params)
    ),
  addKeyword: (keywordData) =>
    apiCall(
      () => api.post("/keywords", keywordData),
      () => mockAPI.keywords.addKeyword(keywordData)
    ),
  updateKeyword: (id, keywordData) =>
    apiCall(
      () => api.put(`/keywords/${id}`, keywordData),
      () => mockAPI.keywords.updateKeyword(id, keywordData)
    ),
  deleteKeyword: (id) =>
    apiCall(
      () => api.delete(`/keywords/${id}`),
      () => mockAPI.keywords.deleteKeyword(id)
    ),
  getKeywordsByPlugin: (pluginId) =>
    apiCall(
      () => api.get(`/keywords/plugin/${pluginId}`),
      () => mockAPI.keywords.getKeywordsByPlugin(pluginId)
    ),
  updateKeywordRanking: (id, rankingData) =>
    apiCall(
      () => api.put(`/keywords/${id}/ranking`, rankingData),
      () => mockAPI.keywords.updateKeyword(id, rankingData)
    ),
};

export default api;
