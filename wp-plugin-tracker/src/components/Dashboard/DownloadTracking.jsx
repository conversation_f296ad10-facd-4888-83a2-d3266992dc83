import React, { useState, useEffect } from "react";
import {
  FiTrendingUp,
  FiTrendingDown,
  FiRefreshCw,
  FiBarChart2,
} from "react-icons/fi";
import { useData } from "../../contexts/DataContext";
import { formatNumber } from "../../utils/formatters";
import { toast } from "react-hot-toast";
import PluginChartModal from "./PluginChartModal";

const DownloadTracking = () => {
  const {
    plugins,
    downloadData,
    loading,
    refreshing,
    refreshAllData,
    fetchAllDownloadData,
  } = useData();

  const [selectedPluginForChart, setSelectedPluginForChart] = useState(null);
  const [showChartModal, setShowChartModal] = useState(false);
  const [loadingChart, setLoadingChart] = useState(false);

  // Fetch download data on mount if not already available
  useEffect(() => {
    if (plugins.length > 0 && Object.keys(downloadData).length === 0) {
      fetchAllDownloadData();
    }
  }, [plugins, downloadData, fetchAllDownloadData]);

  const handleRefresh = async () => {
    try {
      await refreshAllData();
    } catch (error) {
      console.error("Error refreshing data:", error);
    }
  };

  const handleOpenChartModal = async (plugin) => {
    setLoadingChart(true);
    setSelectedPluginForChart(plugin);
    setShowChartModal(true);
    // Small delay to show loading state
    setTimeout(() => setLoadingChart(false), 500);
  };

  const handleCloseChartModal = () => {
    setSelectedPluginForChart(null);
    setShowChartModal(false);
    setLoadingChart(false);
  };

  const formatDateForDisplay = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const getYesterdayAndDayBefore = (slug) => {
    const wpStats = downloadData[slug];
    if (!wpStats || typeof wpStats !== "object") {
      return {
        yesterday: null,
        dayBefore: null,
        change: null,
        percentage: null,
      };
    }

    // Calculate dynamic dates based on current date
    // This ensures the comparison is always for the most recent available days
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const dayBefore = new Date(today);
    dayBefore.setDate(today.getDate() - 2);

    // Format dates as YYYY-MM-DD to match WordPress.org API format
    const yesterdayDate = yesterday.toISOString().split("T")[0];
    const dayBeforeDate = dayBefore.toISOString().split("T")[0];

    // Get download counts for specific dates
    const yesterdayDownloads = parseInt(wpStats[yesterdayDate]) || 0;
    const dayBeforeDownloads = parseInt(wpStats[dayBeforeDate]) || 0;

    // Check if we have data for both dates
    if (!wpStats[yesterdayDate] || !wpStats[dayBeforeDate]) {
      return {
        yesterday: null,
        dayBefore: null,
        change: null,
        percentage: null,
      };
    }

    const yesterdayData = {
      date: yesterdayDate,
      downloads: yesterdayDownloads,
    };

    const dayBeforeData = {
      date: dayBeforeDate,
      downloads: dayBeforeDownloads,
    };

    const change = yesterdayDownloads - dayBeforeDownloads;
    const percentage =
      dayBeforeDownloads > 0 ? (change / dayBeforeDownloads) * 100 : 0;

    return {
      yesterday: yesterdayData,
      dayBefore: dayBeforeData,
      change,
      percentage,
    };
  };

  const PluginCard = ({ plugin }) => {
    const { yesterday, dayBefore, change, percentage } =
      getYesterdayAndDayBefore(plugin.slug);

    return (
      <div className="card">
        {/* Card Header */}
        <div className="mb-4">
          <h3
            className="text-lg font-semibold text-gray-900 truncate"
            title={plugin.name}
          >
            {plugin.name}
          </h3>
        </div>

        {/* Download Comparison Data */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          {!yesterday || !dayBefore ? (
            <div className="text-center py-4">
              <p className="text-gray-500 text-sm">
                Insufficient data for comparison
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-3">
              {/* Yesterday */}
              <div className="text-center">
                <p className="text-xs font-medium text-gray-600 mb-1">
                  Yesterday ({formatDateForDisplay(yesterday.date)})
                </p>
                <div className="bg-green-50 rounded-lg p-2">
                  <p className="text-sm font-bold text-green-900">
                    {yesterday.downloads.toLocaleString()}
                  </p>
                </div>
              </div>

              {/* Day Before Yesterday */}
              <div className="text-center">
                <p className="text-xs font-medium text-gray-600 mb-1">
                  Day Before ({formatDateForDisplay(dayBefore.date)})
                </p>
                <div className="bg-gray-100 rounded-lg p-2">
                  <p className="text-sm font-bold text-gray-900">
                    {dayBefore.downloads.toLocaleString()}
                  </p>
                </div>
              </div>

              {/* Change Analysis */}
              <div className="text-center">
                <p className="text-xs font-medium text-gray-600 mb-1">Change</p>
                <div className="bg-white border rounded-lg p-2">
                  <div className="flex items-center justify-center">
                    {formatChangeDisplay(change, percentage)}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Chart Button at Bottom */}
        <div className="text-center">
          <button
            onClick={() => handleOpenChartModal(plugin)}
            className="btn-primary flex items-center justify-center text-sm px-4 py-2 w-full"
            title="View Charts & Analytics"
          >
            <FiBarChart2 className="w-4 h-4 mr-2" />
            View Charts & Analytics
          </button>
        </div>
      </div>
    );
  };

  const formatChangeDisplay = (change, percentage) => {
    if (change === null || percentage === null) return null;

    const isPositive = change >= 0;
    const formattedChange = Math.abs(change).toLocaleString();
    const formattedPercentage = Math.abs(percentage).toFixed(2);

    return (
      <div
        className={`flex flex-col items-center ${
          isPositive ? "text-green-600" : "text-red-600"
        }`}
      >
        <div className="flex items-center mb-1">
          {isPositive ? (
            <FiTrendingUp className="w-3 h-3 mr-1" />
          ) : (
            <FiTrendingDown className="w-3 h-3 mr-1" />
          )}
          <span className="text-sm font-bold">
            {isPositive ? "+" : "-"}
            {formattedChange}
          </span>
        </div>
        <div className="text-xs font-medium">
          {isPositive ? "+" : "-"}
          {formattedPercentage}%
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="card">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            Download Tracking
          </h2>
          <p className="text-sm text-gray-600">
            Monitor daily download trends and compare yesterday vs day before
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="btn-secondary flex items-center"
        >
          <FiRefreshCw
            className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
          />
          Refresh All Data
        </button>
      </div>

      {/* Plugin Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {plugins.map((plugin) => (
          <PluginCard key={plugin.slug} plugin={plugin} />
        ))}
      </div>

      {plugins.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No plugins found</p>
        </div>
      )}

      {/* Chart Modal */}
      <PluginChartModal
        plugin={selectedPluginForChart}
        isOpen={showChartModal}
        onClose={handleCloseChartModal}
      />
    </div>
  );
};

export default DownloadTracking;
