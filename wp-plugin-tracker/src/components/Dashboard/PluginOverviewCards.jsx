import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { Link } from "react-router-dom";
import {
  FiDownload,
  FiStar,
  FiUsers,
  FiMessageCircle,
  FiExternalLink,
  FiBarChart2,
  FiTrendingUp,
  FiTrendingDown,
  FiCalendar,
  FiRefreshCw,
} from "react-icons/fi";
import { useData } from "../../contexts/DataContext";
import { formatNumber, formatRealNumber } from "../../utils/formatters";
import PluginIcon from "../Common/PluginIcon";
import PluginChartModal from "./PluginChartModal";

const PluginOverviewCards = () => {
  const { plugins, downloadData, loading, refreshing, refreshAllData } =
    useData();

  const [selectedPluginForChart, setSelectedPluginForChart] = useState(null);
  const [showChartModal, setShowChartModal] = useState(false);
  const [loadingChart, setLoadingChart] = useState(false);
  const [containerReady, setContainerReady] = useState(false);

  // Check for container availability
  useEffect(() => {
    const checkContainer = () => {
      const container = document.getElementById(
        "plugin-overview-refresh-button-container"
      );
      if (container) {
        setContainerReady(true);
      } else {
        // Retry after a short delay if container is not ready
        setTimeout(checkContainer, 100);
      }
    };

    checkContainer();
  }, []);

  // Render refresh button in the header container using createPortal
  const RefreshButtonPortal = () => {
    if (!containerReady) return null;

    const container = document.getElementById(
      "plugin-overview-refresh-button-container"
    );
    if (!container) return null;

    return createPortal(
      <button
        onClick={handleRefresh}
        disabled={refreshing}
        className="btn-secondary flex items-center px-2 py-1 text-xs"
      >
        <FiRefreshCw
          className={`w-3 h-3 mr-1 ${refreshing ? "animate-spin" : ""}`}
        />
        {refreshing ? "Refreshing..." : "Refresh"}
      </button>,
      container
    );
  };

  const handleRefresh = async () => {
    try {
      await refreshAllData();
    } catch (error) {
      console.error("Error refreshing plugin data:", error);
    }
  };

  const handleOpenChartModal = async (plugin) => {
    setLoadingChart(true);
    setSelectedPluginForChart(plugin);
    setShowChartModal(true);
    // Small delay to show loading state
    setTimeout(() => setLoadingChart(false), 500);
  };

  const handleCloseChartModal = () => {
    setSelectedPluginForChart(null);
    setShowChartModal(false);
    setLoadingChart(false);
  };

  // Process download stats for display
  const getDownloadStats = (slug) => {
    const stats = downloadData[slug];
    if (!stats || typeof stats !== "object") {
      return null;
    }

    // Get yesterday and day before yesterday dates
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dayBeforeYesterday = new Date();
    dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);

    const yesterdayKey = yesterday.toISOString().split("T")[0];
    const dayBeforeYesterdayKey = dayBeforeYesterday
      .toISOString()
      .split("T")[0];

    const yesterdayDownloads = parseInt(stats[yesterdayKey]) || 0;
    const dayBeforeYesterdayDownloads =
      parseInt(stats[dayBeforeYesterdayKey]) || 0;
    const change = yesterdayDownloads - dayBeforeYesterdayDownloads;

    return {
      yesterday: yesterdayDownloads,
      dayBeforeYesterday: dayBeforeYesterdayDownloads,
      change,
      changePercentage:
        dayBeforeYesterdayDownloads > 0
          ? ((change / dayBeforeYesterdayDownloads) * 100).toFixed(1)
          : 0,
    };
  };

  const getLastReleaseInfo = (plugin) => {
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0];
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const releaseDate = new Date(dateStr);
          releaseDate.setHours(0, 0, 0, 0);
          const diffTime = today - releaseDate;
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }
    return { date: null, days: 0 };
  };

  if (loading || plugins.length === 0) {
    return (
      <div>
        <RefreshButtonPortal />
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div
              key={i}
              className="bg-white rounded-lg border border-gray-200 p-4"
            >
              <div className="animate-pulse">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
                    <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2 mb-3">
                  <div className="h-12 bg-gray-200 rounded-md"></div>
                  <div className="h-12 bg-gray-200 rounded-md"></div>
                </div>
                <div className="h-8 bg-gray-200 rounded-md mb-3"></div>
                <div className="flex space-x-1">
                  <div className="flex-1 h-6 bg-gray-200 rounded-md"></div>
                  <div className="flex-1 h-6 bg-gray-200 rounded-md"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      <RefreshButtonPortal />
      {/* Plugin Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
        {plugins.map((plugin) => {
          const downloadStats = getDownloadStats(plugin.slug);
          const releaseInfo = getLastReleaseInfo(plugin);

          return (
            <div
              key={plugin._id}
              className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
            >
              {/* Plugin Header */}
              <div className="p-4 pb-3">
                <div className="flex items-start space-x-3">
                  <PluginIcon
                    plugin={plugin}
                    size="sm"
                    className="flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <h3
                      className="text-sm font-semibold text-gray-900 mb-1 truncate"
                      title={plugin.name}
                    >
                      {plugin.name}
                    </h3>
                    <div className="flex items-center justify-between text-xs text-gray-600">
                      <span>v{plugin.version || "N/A"}</span>
                      <span className="text-purple-600 font-medium">
                        #{plugin.currentRank || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Plugin Stats */}
              <div className="px-4 pb-3">
                {/* Last Release */}
                <div className="bg-purple-50 rounded-md p-3 border border-purple-200">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-purple-700">
                      Release
                    </span>
                    {releaseInfo.date ? (
                      <span className="text-xs font-medium text-purple-900">
                        {new Date(releaseInfo.date).toLocaleDateString(
                          "en-US",
                          {
                            month: "short",
                            day: "numeric",
                            year: "numeric",
                          }
                        )}{" "}
                        ({releaseInfo.days}d)
                      </span>
                    ) : (
                      <span className="text-xs font-medium text-purple-700">
                        No Data
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Download Trend */}
              {downloadStats && (
                <div className="px-4 pb-3">
                  <div className="bg-blue-50 rounded-md p-2 border border-blue-200">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-1">
                        <FiTrendingUp className="w-3 h-3 text-blue-600" />
                        <span className="text-xs font-medium text-blue-800">
                          Download Trend
                        </span>
                      </div>
                      <span
                        className={`px-1.5 py-0.5 rounded-full text-xs font-medium flex items-center space-x-1 ${
                          downloadStats.change > 0
                            ? "bg-green-100 text-green-700"
                            : downloadStats.change < 0
                            ? "bg-red-100 text-red-700"
                            : "bg-gray-100 text-gray-700"
                        }`}
                      >
                        {downloadStats.change > 0 ? (
                          <FiTrendingUp className="w-2.5 h-2.5" />
                        ) : downloadStats.change < 0 ? (
                          <FiTrendingDown className="w-2.5 h-2.5" />
                        ) : null}
                        <span>
                          {downloadStats.change > 0 ? "+" : ""}
                          {downloadStats.changePercentage}%
                        </span>
                      </span>
                    </div>

                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center space-x-1">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          <span className="text-blue-700">Yesterday</span>
                        </div>
                        <span className="font-medium text-blue-900">
                          {formatRealNumber(downloadStats.yesterday)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center space-x-1">
                          <div className="w-1.5 h-1.5 bg-blue-300 rounded-full"></div>
                          <span className="text-blue-700">Day Before</span>
                        </div>
                        <span className="font-medium text-blue-900">
                          {formatRealNumber(downloadStats.dayBeforeYesterday)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between text-xs pt-1 border-t border-blue-200">
                        <div className="flex items-center space-x-1">
                          <div
                            className={`w-1.5 h-1.5 rounded-full ${
                              downloadStats.change > 0
                                ? "bg-green-500"
                                : downloadStats.change < 0
                                ? "bg-red-500"
                                : "bg-gray-400"
                            }`}
                          ></div>
                          <span className="text-blue-700">Change</span>
                        </div>
                        <span
                          className={`font-medium ${
                            downloadStats.change > 0
                              ? "text-green-600"
                              : downloadStats.change < 0
                              ? "text-red-600"
                              : "text-gray-600"
                          }`}
                        >
                          {downloadStats.change > 0 ? "+" : ""}
                          {formatRealNumber(downloadStats.change)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Charts Button */}
              <div className="px-4 pb-4">
                <button
                  onClick={() => handleOpenChartModal(plugin)}
                  className="w-full flex items-center justify-center px-2 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
                >
                  <FiBarChart2 className="w-3 h-3 mr-1" />
                  View Charts & Analytics
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Chart Modal */}
      <PluginChartModal
        plugin={selectedPluginForChart}
        isOpen={showChartModal}
        onClose={handleCloseChartModal}
      />
    </div>
  );
};

export default PluginOverviewCards;
