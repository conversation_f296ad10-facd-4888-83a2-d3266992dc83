import React, { useState } from "react";
import { Link } from "react-router-dom";
import {
  FiDownload,
  FiStar,
  FiUsers,
  FiMessageCircle,
  FiExternalLink,
  FiBarChart2,
  FiTrendingUp,
  FiTrendingDown,
  FiCalendar,
} from "react-icons/fi";
import { useData } from "../../contexts/DataContext";
import { formatNumber, formatRealNumber } from "../../utils/formatters";
import PluginIcon from "../Common/PluginIcon";

const PluginOverviewCards = () => {
  const { plugins, downloadData, loading } = useData();

  // Process download stats for display
  const getDownloadStats = (slug) => {
    const stats = downloadData[slug];
    if (!stats || typeof stats !== "object") {
      return null;
    }

    // Get yesterday and day before yesterday dates
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dayBeforeYesterday = new Date();
    dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);

    const yesterdayKey = yesterday.toISOString().split("T")[0];
    const dayBeforeYesterdayKey = dayBeforeYesterday
      .toISOString()
      .split("T")[0];

    const yesterdayDownloads = parseInt(stats[yesterdayKey]) || 0;
    const dayBeforeYesterdayDownloads =
      parseInt(stats[dayBeforeYesterdayKey]) || 0;
    const change = yesterdayDownloads - dayBeforeYesterdayDownloads;

    return {
      yesterday: yesterdayDownloads,
      dayBeforeYesterday: dayBeforeYesterdayDownloads,
      change,
      changePercentage:
        dayBeforeYesterdayDownloads > 0
          ? ((change / dayBeforeYesterdayDownloads) * 100).toFixed(1)
          : 0,
    };
  };

  const getLastReleaseInfo = (plugin) => {
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0];
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const releaseDate = new Date(dateStr);
          releaseDate.setHours(0, 0, 0, 0);
          const diffTime = today - releaseDate;
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }
    return { date: null, days: 0 };
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div
            key={i}
            className="bg-white rounded-lg border border-gray-200 p-4"
          >
            <div className="animate-pulse">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 mb-3">
                <div className="h-12 bg-gray-200 rounded-md"></div>
                <div className="h-12 bg-gray-200 rounded-md"></div>
              </div>
              <div className="h-8 bg-gray-200 rounded-md mb-3"></div>
              <div className="flex space-x-1">
                <div className="flex-1 h-6 bg-gray-200 rounded-md"></div>
                <div className="flex-1 h-6 bg-gray-200 rounded-md"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
      {plugins.map((plugin) => {
        const downloadStats = getDownloadStats(plugin.slug);
        const releaseInfo = getLastReleaseInfo(plugin);

        return (
          <div
            key={plugin._id}
            className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
          >
            {/* Plugin Header */}
            <div className="p-4 pb-3">
              <div className="flex items-start space-x-3">
                <PluginIcon
                  plugin={plugin}
                  size="sm"
                  className="flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h3
                    className="text-sm font-semibold text-gray-900 mb-1 truncate"
                    title={plugin.name}
                  >
                    {plugin.name}
                  </h3>
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>v{plugin.version || "N/A"}</span>
                    <span className="text-purple-600 font-medium">
                      #{plugin.currentRank || "N/A"}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Plugin Stats */}
            <div className="px-4 pb-3">
              {/* Last Release */}
              <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <FiCalendar className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-sm font-semibold text-purple-800">
                      Last Release
                    </span>
                  </div>
                  {releaseInfo.date && (
                    <div className="px-2 py-1 bg-purple-200 rounded-full">
                      <span className="text-xs font-medium text-purple-700">
                        {releaseInfo.days < 30
                          ? "Recent"
                          : releaseInfo.days < 90
                          ? "Moderate"
                          : "Old"}
                      </span>
                    </div>
                  )}
                </div>
                <div className="text-purple-900">
                  {releaseInfo.date ? (
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-2xl font-bold text-purple-900 mb-1">
                          {releaseInfo.days}
                          <span className="text-sm font-normal text-purple-600 ml-1">
                            days ago
                          </span>
                        </p>
                        <p className="text-sm text-purple-700 font-medium">
                          {new Date(releaseInfo.date).toLocaleDateString(
                            "en-US",
                            {
                              weekday: "long",
                              month: "long",
                              day: "numeric",
                              year: "numeric",
                            }
                          )}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-sm">
                          <span className="text-lg">📅</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <div className="w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mx-auto mb-2">
                        <span className="text-2xl">❓</span>
                      </div>
                      <p className="text-lg font-bold text-purple-900">
                        No release data
                      </p>
                      <p className="text-sm text-purple-600 mt-1">
                        Release information not available
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Download Trend */}
            {downloadStats && (
              <div className="px-4 pb-3">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <FiTrendingUp className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-semibold text-blue-800">
                        Download Trend
                      </span>
                    </div>
                    <div
                      className={`px-3 py-1 rounded-full flex items-center space-x-1 ${
                        downloadStats.change > 0
                          ? "bg-green-100 text-green-700"
                          : downloadStats.change < 0
                          ? "bg-red-100 text-red-700"
                          : "bg-gray-100 text-gray-700"
                      }`}
                    >
                      {downloadStats.change > 0 ? (
                        <FiTrendingUp className="w-3 h-3" />
                      ) : downloadStats.change < 0 ? (
                        <FiTrendingDown className="w-3 h-3" />
                      ) : null}
                      <span className="text-xs font-medium">
                        {downloadStats.change > 0 ? "+" : ""}
                        {downloadStats.changePercentage}%
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div className="bg-white rounded-lg p-3 border border-blue-100">
                      <div className="flex items-center space-x-2 mb-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-xs font-medium text-blue-700">
                          Yesterday
                        </span>
                      </div>
                      <p className="text-lg font-bold text-blue-900">
                        {formatRealNumber(downloadStats.yesterday)}
                      </p>
                    </div>

                    <div className="bg-white rounded-lg p-3 border border-blue-100">
                      <div className="flex items-center space-x-2 mb-1">
                        <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
                        <span className="text-xs font-medium text-blue-700">
                          Day Before
                        </span>
                      </div>
                      <p className="text-lg font-bold text-blue-900">
                        {formatRealNumber(downloadStats.dayBeforeYesterday)}
                      </p>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg p-3 border border-blue-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className={`w-3 h-3 rounded-full ${
                            downloadStats.change > 0
                              ? "bg-green-500"
                              : downloadStats.change < 0
                              ? "bg-red-500"
                              : "bg-gray-400"
                          }`}
                        ></div>
                        <span className="text-sm font-medium text-gray-700">
                          Net Change
                        </span>
                      </div>
                      <div className="text-right">
                        <p
                          className={`text-lg font-bold ${
                            downloadStats.change > 0
                              ? "text-green-600"
                              : downloadStats.change < 0
                              ? "text-red-600"
                              : "text-gray-600"
                          }`}
                        >
                          {downloadStats.change > 0 ? "+" : ""}
                          {formatRealNumber(downloadStats.change)}
                        </p>
                        <p className="text-xs text-gray-500">downloads</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="px-4 pb-4">
              <div className="flex space-x-1">
                <Link
                  to={`/analytics?plugin=${plugin._id}`}
                  className="flex-1 flex items-center justify-center px-2 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
                >
                  <FiBarChart2 className="w-3 h-3 mr-1" />
                  Analytics
                </Link>
                <a
                  href={`https://wordpress.org/plugins/${plugin.slug}/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <FiExternalLink className="w-3 h-3 mr-1" />
                  View
                </a>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PluginOverviewCards;
