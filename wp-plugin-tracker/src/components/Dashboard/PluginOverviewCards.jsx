import React, { useState } from "react";
import { Link } from "react-router-dom";
import {
  FiDownload,
  FiStar,
  FiUsers,
  FiMessageCircle,
  FiExternalLink,
  FiBarChart2,
  FiTrendingUp,
  FiTrendingDown,
  FiCalendar,
} from "react-icons/fi";
import { useData } from "../../contexts/DataContext";
import { formatNumber, formatRealNumber } from "../../utils/formatters";
import PluginIcon from "../Common/PluginIcon";

const PluginOverviewCards = () => {
  const { plugins, downloadData, loading } = useData();

  // Process download stats for display
  const getDownloadStats = (slug) => {
    const stats = downloadData[slug];
    if (!stats || typeof stats !== "object") {
      return null;
    }

    // Get yesterday and day before yesterday dates
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dayBeforeYesterday = new Date();
    dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);

    const yesterdayKey = yesterday.toISOString().split("T")[0];
    const dayBeforeYesterdayKey = dayBeforeYesterday
      .toISOString()
      .split("T")[0];

    const yesterdayDownloads = parseInt(stats[yesterdayKey]) || 0;
    const dayBeforeYesterdayDownloads =
      parseInt(stats[dayBeforeYesterdayKey]) || 0;
    const change = yesterdayDownloads - dayBeforeYesterdayDownloads;

    return {
      yesterday: yesterdayDownloads,
      dayBeforeYesterday: dayBeforeYesterdayDownloads,
      change,
      changePercentage:
        dayBeforeYesterdayDownloads > 0
          ? ((change / dayBeforeYesterdayDownloads) * 100).toFixed(1)
          : 0,
    };
  };

  const getLastReleaseInfo = (plugin) => {
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0];
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const releaseDate = new Date(dateStr);
          releaseDate.setHours(0, 0, 0, 0);
          const diffTime = today - releaseDate;
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }
    return { date: null, days: 0 };
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div
            key={i}
            className="bg-white rounded-lg border border-gray-200 p-4"
          >
            <div className="animate-pulse">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 mb-3">
                <div className="h-12 bg-gray-200 rounded-md"></div>
                <div className="h-12 bg-gray-200 rounded-md"></div>
                <div className="h-12 bg-gray-200 rounded-md"></div>
                <div className="h-12 bg-gray-200 rounded-md"></div>
              </div>
              <div className="h-8 bg-gray-200 rounded-md mb-3"></div>
              <div className="flex space-x-1">
                <div className="flex-1 h-6 bg-gray-200 rounded-md"></div>
                <div className="flex-1 h-6 bg-gray-200 rounded-md"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
      {plugins.map((plugin) => {
        const downloadStats = getDownloadStats(plugin.slug);
        const releaseInfo = getLastReleaseInfo(plugin);

        return (
          <div
            key={plugin._id}
            className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
          >
            {/* Plugin Header */}
            <div className="p-4 pb-3">
              <div className="flex items-start space-x-3">
                <PluginIcon
                  plugin={plugin}
                  size="sm"
                  className="flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h3
                    className="text-sm font-semibold text-gray-900 mb-1 truncate"
                    title={plugin.name}
                  >
                    {plugin.name}
                  </h3>
                  <p className="text-xs text-gray-600 mb-1">
                    v{plugin.version || "N/A"}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <FiCalendar className="w-3 h-3" />
                      <span>
                        {releaseInfo.date
                          ? `${releaseInfo.days}d`
                          : "No release"}
                      </span>
                    </div>
                    <span className="text-purple-600 font-medium">
                      #{plugin.currentRank || "N/A"}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Plugin Stats */}
            <div className="px-4 pb-3">
              <div className="grid grid-cols-2 gap-2">
                {/* Total Downloads */}
                <div className="bg-blue-50 rounded-md p-2">
                  <div className="flex items-center space-x-1 mb-1">
                    <FiDownload className="w-3 h-3 text-blue-600" />
                    <span className="text-xs font-medium text-blue-700">
                      Downloads
                    </span>
                  </div>
                  <p className="text-sm font-bold text-blue-900">
                    {formatNumber(plugin.downloads)}
                  </p>
                </div>

                {/* Rating */}
                <div className="bg-yellow-50 rounded-md p-2">
                  <div className="flex items-center space-x-1 mb-1">
                    <FiStar className="w-3 h-3 text-yellow-600" />
                    <span className="text-xs font-medium text-yellow-700">
                      Rating
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <p className="text-sm font-bold text-yellow-900">
                      {plugin.rating ? plugin.rating.toFixed(1) : "0.0"}
                    </p>
                    <span className="text-xs text-yellow-700">
                      ({formatNumber(plugin.ratingCount || 0)})
                    </span>
                  </div>
                </div>

                {/* Active Installs */}
                <div className="bg-green-50 rounded-md p-2">
                  <div className="flex items-center space-x-1 mb-1">
                    <FiUsers className="w-3 h-3 text-green-600" />
                    <span className="text-xs font-medium text-green-700">
                      Installs
                    </span>
                  </div>
                  <p className="text-sm font-bold text-green-900">
                    {formatNumber(plugin.activeInstalls)}
                  </p>
                </div>

                {/* Support Topics */}
                <div className="bg-red-50 rounded-md p-2">
                  <div className="flex items-center space-x-1 mb-1">
                    <FiMessageCircle className="w-3 h-3 text-red-600" />
                    <span className="text-xs font-medium text-red-700">
                      Support
                    </span>
                  </div>
                  <p className="text-sm font-bold text-red-900">
                    {plugin.supportTickets?.unresolvedTopics || 0}
                  </p>
                </div>
              </div>
            </div>

            {/* Download Trend */}
            {downloadStats && (
              <div className="px-4 pb-3">
                <div className="bg-gray-50 rounded-md p-2">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-700">
                      Daily Trend
                    </span>
                    <div className="flex items-center space-x-1">
                      {downloadStats.change > 0 ? (
                        <FiTrendingUp className="w-3 h-3 text-green-500" />
                      ) : downloadStats.change < 0 ? (
                        <FiTrendingDown className="w-3 h-3 text-red-500" />
                      ) : null}
                      <span
                        className={`text-xs font-medium ${
                          downloadStats.change > 0
                            ? "text-green-600"
                            : downloadStats.change < 0
                            ? "text-red-600"
                            : "text-gray-600"
                        }`}
                      >
                        {downloadStats.change > 0 ? "+" : ""}
                        {downloadStats.changePercentage}%
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <div>
                      <span className="text-gray-600">Yesterday: </span>
                      <span className="font-semibold text-gray-900">
                        {formatRealNumber(downloadStats.yesterday)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Change: </span>
                      <span
                        className={`font-semibold ${
                          downloadStats.change > 0
                            ? "text-green-600"
                            : downloadStats.change < 0
                            ? "text-red-600"
                            : "text-gray-600"
                        }`}
                      >
                        {downloadStats.change > 0 ? "+" : ""}
                        {formatRealNumber(downloadStats.change)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="px-4 pb-4">
              <div className="flex space-x-1">
                <Link
                  to={`/analytics?plugin=${plugin._id}`}
                  className="flex-1 flex items-center justify-center px-2 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
                >
                  <FiBarChart2 className="w-3 h-3 mr-1" />
                  Analytics
                </Link>
                <a
                  href={`https://wordpress.org/plugins/${plugin.slug}/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <FiExternalLink className="w-3 h-3 mr-1" />
                  View
                </a>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PluginOverviewCards;
