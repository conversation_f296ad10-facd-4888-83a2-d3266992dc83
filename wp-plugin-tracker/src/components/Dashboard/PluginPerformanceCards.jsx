import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { Link } from "react-router-dom";
import {
  FiTrendingUp,
  FiTrendingDown,
  FiBarChart2,
  FiPieChart,
  FiCalendar,
  FiTag,
  FiRefreshCw,
} from "react-icons/fi";
import { useData } from "../../contexts/DataContext";
import {
  formatNumber,
  formatRealNumber,
  formatRelativeTime,
} from "../../utils/formatters";
import PluginIcon from "../Common/PluginIcon";
import PluginChartModal from "./PluginChartModal";
import { toast } from "react-hot-toast";

const PluginPerformanceCards = () => {
  const {
    plugins,
    downloadData,
    loading,
    refreshing,
    refreshAllData,
    fetchAllDownloadData,
  } = useData();

  const [selectedPluginForChart, setSelectedPluginForChart] = useState(null);
  const [showChartModal, setShowChartModal] = useState(false);
  const [loadingChart, setLoadingChart] = useState(false);

  // Fetch download data on mount if not already available
  useEffect(() => {
    if (plugins.length > 0 && Object.keys(downloadData).length === 0) {
      fetchAllDownloadData();
    }
  }, [plugins, downloadData, fetchAllDownloadData]);

  // Render refresh button in the header container using createPortal
  const RefreshButtonPortal = () => {
    const container = document.getElementById("refresh-button-container");
    if (!container) return null;

    return createPortal(
      <button
        onClick={handleRefresh}
        disabled={refreshing}
        className="btn-secondary flex items-center px-2 py-1 text-xs"
      >
        <FiRefreshCw
          className={`w-3 h-3 mr-1 ${refreshing ? "animate-spin" : ""}`}
        />
        {refreshing ? "Refreshing..." : "Refresh"}
      </button>,
      container
    );
  };

  // Process download stats for display
  const getDownloadStats = (slug) => {
    const stats = downloadData[slug];
    if (!stats || typeof stats !== "object") {
      return null;
    }

    // Get yesterday and day before yesterday dates
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dayBeforeYesterday = new Date();
    dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);

    const yesterdayKey = yesterday.toISOString().split("T")[0];
    const dayBeforeYesterdayKey = dayBeforeYesterday
      .toISOString()
      .split("T")[0];

    const yesterdayDownloads = parseInt(stats[yesterdayKey]) || 0;
    const dayBeforeYesterdayDownloads =
      parseInt(stats[dayBeforeYesterdayKey]) || 0;
    const change = yesterdayDownloads - dayBeforeYesterdayDownloads;

    return {
      yesterday: yesterdayDownloads,
      dayBeforeYesterday: dayBeforeYesterdayDownloads,
      change,
      changePercentage:
        dayBeforeYesterdayDownloads > 0
          ? ((change / dayBeforeYesterdayDownloads) * 100).toFixed(1)
          : 0,
    };
  };

  const handleRefresh = async () => {
    try {
      await refreshAllData();
    } catch (error) {
      console.error("Error refreshing plugin data:", error);
    }
  };

  const handleOpenChartModal = async (plugin) => {
    setLoadingChart(true);
    setSelectedPluginForChart(plugin);
    setShowChartModal(true);
    // Small delay to show loading state
    setTimeout(() => setLoadingChart(false), 500);
  };

  const handleCloseChartModal = () => {
    setSelectedPluginForChart(null);
    setShowChartModal(false);
    setLoadingChart(false);
  };

  const getLastReleaseInfo = (plugin) => {
    // Try to get from new fields first
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0];

          // Calculate days difference properly
          const today = new Date();
          today.setHours(0, 0, 0, 0); // Reset time to start of day

          const releaseDate = new Date(dateStr);
          releaseDate.setHours(0, 0, 0, 0); // Reset time to start of day

          const diffTime = today - releaseDate;
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    // Try to extract from lastUpdated field
    if (plugin.lastUpdated) {
      try {
        const match = plugin.lastUpdated.match(/(\d{4}-\d{2}-\d{2})/);
        if (match) {
          const dateStr = match[1];

          // Calculate days difference properly
          const today = new Date();
          today.setHours(0, 0, 0, 0); // Reset time to start of day

          const releaseDate = new Date(dateStr);
          releaseDate.setHours(0, 0, 0, 0); // Reset time to start of day

          const diffTime = today - releaseDate;
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    return { date: null, days: 0 };
  };

  if (loading) {
    return (
      <div className="card">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <RefreshButtonPortal />
      {/* Plugin Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6">
        {plugins.map((plugin) => {
          const releaseInfo = getLastReleaseInfo(plugin);
          const downloadStats = getDownloadStats(plugin.slug);

          return (
            <div
              key={plugin._id}
              className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
            >
              {/* Compact Plugin Info */}
              <div className="p-3">
                <div className="flex items-center space-x-2 mb-2 pb-2 border-b border-gray-200">
                  <PluginIcon
                    plugin={plugin}
                    size="xs"
                    className="flex-shrink-0"
                  />
                  <h3
                    className="text-sm font-semibold text-gray-900 truncate flex-1"
                    title={plugin.name}
                  >
                    {plugin.name}
                  </h3>
                </div>

                {/* Compact Info Grid */}
                <div className="space-y-2 text-xs">
                  <div className="grid grid-cols-2 gap-x-4">
                    <div className="flex items-center space-x-1">
                      <span className="text-gray-500">Rank:</span>
                      <span className="font-medium text-purple-600">
                        #{plugin.currentRank || "N/A"}
                      </span>
                    </div>
                    <div className="flex items-center justify-end space-x-1">
                      <span className="text-gray-500">Version:</span>
                      <span className="font-medium text-blue-600">
                        {plugin.version || "N/A"}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Release:</span>
                    <span className="font-medium text-green-600">
                      {releaseInfo.date
                        ? `${releaseInfo.date} (${releaseInfo.days}d ago)`
                        : "Never"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Compact Download Stats */}
              <div className="px-3 py-2 bg-gray-50 border-y border-gray-100">
                {downloadStats ? (
                  <div className="space-y-1.5">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600">Yesterday:</span>
                      <span className="font-semibold text-gray-900">
                        {formatRealNumber(downloadStats.yesterday)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600">Day Before:</span>
                      <span className="font-medium text-gray-700">
                        {formatRealNumber(downloadStats.dayBeforeYesterday)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-xs pt-1.5 border-t border-gray-200">
                      <span className="text-gray-600">Change:</span>
                      <div className="flex items-center space-x-1">
                        {downloadStats.change > 0 ? (
                          <FiTrendingUp className="w-3 h-3 text-green-500" />
                        ) : downloadStats.change < 0 ? (
                          <FiTrendingDown className="w-3 h-3 text-red-500" />
                        ) : null}
                        <span
                          className={`font-medium ${
                            downloadStats.change > 0
                              ? "text-green-600"
                              : downloadStats.change < 0
                              ? "text-red-600"
                              : "text-gray-600"
                          }`}
                        >
                          {downloadStats.change > 0 ? "+" : ""}
                          {formatRealNumber(downloadStats.change)} (
                          {downloadStats.changePercentage > 0 ? "+" : ""}
                          {downloadStats.changePercentage}%)
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-1.5">
                    <p className="text-xs text-gray-400">No download data</p>
                  </div>
                )}
              </div>

              {/* Charts Button */}
              <div className="p-3">
                <button
                  onClick={() => handleOpenChartModal(plugin)}
                  className="w-full flex items-center justify-center px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
                >
                  <FiBarChart2 className="w-3 h-3 mr-1" />
                  View Charts & Analytics
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Chart Modal */}
      <PluginChartModal
        plugin={selectedPluginForChart}
        isOpen={showChartModal}
        onClose={handleCloseChartModal}
      />
    </div>
  );
};

export default PluginPerformanceCards;
