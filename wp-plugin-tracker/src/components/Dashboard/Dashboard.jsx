import React from "react";
import { <PERSON> } from "react-router-dom";
import { FiPlus, FiBarChart2, FiT<PERSON>ding<PERSON>p, FiCalendar } from "react-icons/fi";
import { useAuth } from "../../contexts/AuthContext";
import { useData } from "../../contexts/DataContext";
import PluginDashboardTable from "./PluginDashboardTable";
import PluginPerformanceCards from "./PluginPerformanceCards";
import PluginOverviewCards from "./PluginOverviewCards";
import DownloadTracking from "./DownloadTracking";
import DataStatus from "../Common/DataStatus";

const Dashboard = () => {
  const { user } = useAuth();
  const { lastUpdated, isCacheValid } = useData();

  return (
    <div className="space-y-4">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-1">
              Plugin Dashboard
            </h1>
            <p className="text-sm text-gray-600">
              Welcome back,{" "}
              <span className="font-semibold text-blue-700">{user?.name}</span>!
              👋
              <span className="ml-2 text-gray-500">
                Monitor your WordPress plugins in real-time
              </span>
            </p>
            {lastUpdated && (
              <p className="text-xs text-gray-500 mt-1">
                Last updated: {lastUpdated.toLocaleString()}
                {!isCacheValid && (
                  <span className="ml-2 text-orange-600 font-medium">
                    (Data may be stale)
                  </span>
                )}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-3">
            <DataStatus />
            <Link
              to="/plugins"
              className="btn-primary flex items-center px-4 py-2 text-sm font-medium"
            >
              <FiPlus className="w-4 h-4 mr-2" />
              Add Plugin
            </Link>
          </div>
        </div>
      </div>

      {/* Plugin Performance Dashboard - HIDDEN */}
      {/* <div className="mb-6">
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 lg:col-span-8">
            <PluginDashboardTable />
          </div>
        </div>
      </div> */}

      {/* Plugin Performance Overview Cards */}
      <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
        <div className="flex items-center justify-between mb-3 pb-3 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-md flex items-center justify-center">
              <FiBarChart2 className="w-3 h-3 text-white" />
            </div>
            <div>
              <h2 className="text-base font-bold text-gray-900">
                Plugin Performance Overview
              </h2>
              <p className="text-xs text-gray-500">
                Real-time insights and metrics
              </p>
            </div>
          </div>
          <div id="refresh-button-container"></div>
        </div>
        <PluginPerformanceCards />
      </div>

      {/* Plugin Overview Cards */}
      <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
        <div className="flex items-center justify-between mb-3 pb-3 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-teal-600 rounded-md flex items-center justify-center">
              <FiTrendingUp className="w-3 h-3 text-white" />
            </div>
            <div>
              <h2 className="text-base font-bold text-gray-900">
                Plugin Overview
              </h2>
              <p className="text-xs text-gray-500">
                Detailed plugin statistics and metrics
              </p>
            </div>
          </div>
        </div>
        <PluginOverviewCards />
      </div>

      {/* Download Tracking - HIDDEN */}
      {/* <div className="w-full">
        <DownloadTracking />
      </div> */}
    </div>
  );
};

export default Dashboard;
