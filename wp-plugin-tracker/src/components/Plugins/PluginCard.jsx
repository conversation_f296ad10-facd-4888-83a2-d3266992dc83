import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  FiDownload,
  FiStar,
  FiCalendar,
  FiExternalLink,
  FiRefreshCw,
  FiEdit,
  FiTrash2,
  FiMessageCircle,
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiTrendingUp,
  FiUsers,
} from "react-icons/fi";
import PluginIcon from "../Common/PluginIcon";
import {
  formatNumber,
  formatRelativeTime,
  getStatusBadgeColor,
  getPluginBannerUrl,
} from "../../utils/formatters";

const PluginCard = ({
  plugin,
  onRefresh,
  onEdit,
  onDelete,
  isRefreshing = false,
}) => {
  const [imageError, setImageError] = useState(false);

  const bannerUrl = getPluginBannerUrl(plugin);

  // Helper function to extract date and calculate days
  const getLastReleaseInfo = (plugin) => {
    // Try to get from new fields first
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0]; // Get YYYY-MM-DD format
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    // Try to extract from lastUpdated field (WordPress.org format)
    if (plugin.lastUpdated) {
      try {
        // Extract date from WordPress.org format like "2025-05-14 10:31am GMT"
        const match = plugin.lastUpdated.match(/(\d{4}-\d{2}-\d{2})/);
        if (match) {
          const dateStr = match[1]; // This gives us "2025-05-14"
          const date = new Date(dateStr + "T00:00:00.000Z");
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    return { date: null, days: 0 };
  };

  const releaseInfo = getLastReleaseInfo(plugin);

  return (
    <div className="plugin-card bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
      {/* Plugin Banner/Header */}
      <div className="relative h-32 bg-gradient-to-r from-primary-500 to-primary-600 overflow-hidden">
        {bannerUrl && !imageError ? (
          <img
            src={bannerUrl}
            alt={`${plugin.name} banner`}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : null}
        <div
          className={`absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center ${
            bannerUrl && !imageError ? "hidden" : "block"
          }`}
        >
          <div className="text-center text-white">
            <PluginIcon
              plugin={plugin}
              size="md"
              className="mx-auto mb-2 bg-white/20 backdrop-blur-sm"
            />
            <h3 className="font-medium text-sm truncate px-4">{plugin.name}</h3>
          </div>
        </div>

        {/* Status Badge */}
        <div className="absolute top-3 right-3">
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize ${getStatusBadgeColor(
              plugin.status
            )}`}
          >
            {plugin.status || "unknown"}
          </span>
        </div>
      </div>

      {/* Plugin Content */}
      <div className="p-3">
        {/* Plugin Info */}
        <div className="flex items-start space-x-2 mb-3">
          <PluginIcon plugin={plugin} size="sm" className="mt-0.5" />
          <div className="flex-1 min-w-0">
            <h3
              className="font-semibold text-gray-900 truncate text-sm"
              title={plugin.name}
            >
              {plugin.name}
            </h3>
            <p className="text-xs text-gray-500 truncate" title={plugin.slug}>
              {plugin.slug}
            </p>
            {plugin.shortDescription && (
              <p
                className="text-xs text-gray-600 mt-1 line-clamp-2"
                title={plugin.shortDescription}
              >
                {plugin.shortDescription}
              </p>
            )}
          </div>
        </div>

        {/* Stats - 3 columns layout */}
        <div className="mb-3">
          <div className="grid grid-cols-3 gap-1 text-xs">
            {/* Column 1 - Total Downloads */}
            <div className="flex flex-col items-center text-center p-2 bg-blue-50 rounded-lg">
              <FiDownload className="w-3 h-3 text-blue-500 mb-1" />
              <span className="font-medium text-gray-900 text-xs">
                {formatNumber(plugin.downloads)}
              </span>
              <span className="text-xs text-gray-500">downloads</span>
            </div>

            {/* Column 2 - Active Installs */}
            <div className="flex flex-col items-center text-center p-2 bg-green-50 rounded-lg">
              <FiUsers className="w-3 h-3 text-green-500 mb-1" />
              <span className="font-medium text-gray-900 text-xs">
                {formatNumber(plugin.activeInstalls || 0)}
              </span>
              <span className="text-xs text-gray-500">installs</span>
            </div>

            {/* Column 3 - Rating */}
            <div className="flex flex-col items-center text-center p-2 bg-yellow-50 rounded-lg">
              <FiStar className="w-3 h-3 text-yellow-500 mb-1" />
              <span className="font-medium text-gray-900 text-xs">
                {plugin.rating || "0.0"}
              </span>
              <span className="text-xs text-gray-500">
                ({plugin.ratingCount || 0})
              </span>
            </div>
          </div>

          {/* Second row - 2 columns for remaining info */}
          <div className="grid grid-cols-2 gap-1 text-xs mt-2">
            {/* Plugin Rank */}
            <div className="flex flex-col items-center text-center p-2 bg-purple-50 rounded-lg">
              <FiTrendingUp className="w-3 h-3 text-purple-500 mb-1" />
              <span className="font-medium text-gray-900 text-xs">
                #{plugin.pluginRank || "N/A"}
              </span>
              <span className="text-xs text-gray-500">rank</span>
            </div>

            {/* Last Release */}
            <div className="flex flex-col items-center text-center p-2 bg-orange-50 rounded-lg">
              <FiCalendar className="w-3 h-3 text-orange-500 mb-1" />
              <span className="font-medium text-gray-900 text-xs">
                {releaseInfo.date ? `${releaseInfo.days}d ago` : "Never"}
              </span>
              <span className="text-xs text-gray-500">last release</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <Link
            to={`/plugins/${plugin.slug}`}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center"
          >
            <FiExternalLink className="w-4 h-4 mr-1" />
            View Details
          </Link>

          <div className="flex items-center space-x-1">
            <button
              onClick={() => onRefresh(plugin)}
              disabled={isRefreshing}
              className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200 disabled:opacity-50"
              title="Refresh Data"
            >
              <FiRefreshCw
                className={`w-4 h-4 ${isRefreshing ? "animate-spin" : ""}`}
              />
            </button>
            <button
              onClick={() => onEdit(plugin)}
              className="p-1.5 text-yellow-600 hover:bg-yellow-50 rounded-md transition-colors duration-200"
              title="Edit Plugin"
            >
              <FiEdit className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(plugin)}
              className="p-1.5 text-red-600 hover:bg-red-50 rounded-md transition-colors duration-200"
              title="Delete Plugin"
            >
              <FiTrash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PluginCard;
