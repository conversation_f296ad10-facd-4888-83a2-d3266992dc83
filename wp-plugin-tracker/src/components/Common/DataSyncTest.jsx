import React, { useState } from "react";
import { FiR<PERSON>resh<PERSON><PERSON>, FiCheck, FiX, FiInfo } from "react-icons/fi";
import { useData } from "../../contexts/DataContext";
import { dataSyncAPI } from "../../services/api";
import toast from "react-hot-toast";

const DataSyncTest = () => {
  const {
    plugins,
    downloadData,
    loading,
    refreshing,
    lastUpdated,
    isCacheValid,
    cacheAge,
    refreshAllData,
  } = useData();

  const [syncStatus, setSyncStatus] = useState(null);
  const [testResults, setTestResults] = useState([]);

  const runTest = async (testName, testFn) => {
    try {
      const result = await testFn();
      const testResult = {
        name: testName,
        status: "success",
        message: result.message || "Test passed",
        data: result.data,
        timestamp: new Date(),
      };
      setTestResults(prev => [...prev, testResult]);
      return testResult;
    } catch (error) {
      const testResult = {
        name: testName,
        status: "error",
        message: error.message,
        timestamp: new Date(),
      };
      setTestResults(prev => [...prev, testResult]);
      return testResult;
    }
  };

  const testDataContext = async () => {
    return {
      message: `Data Context loaded with ${plugins.length} plugins and ${Object.keys(downloadData).length} download datasets`,
      data: {
        pluginsCount: plugins.length,
        downloadDataCount: Object.keys(downloadData).length,
        loading,
        refreshing,
        lastUpdated,
        isCacheValid,
        cacheAge,
      },
    };
  };

  const testSyncAPI = async () => {
    const status = await dataSyncAPI.getStatus();
    setSyncStatus(status.data);
    return {
      message: "Sync API is accessible",
      data: status.data,
    };
  };

  const testDataRefresh = async () => {
    await refreshAllData();
    return {
      message: "Data refresh completed successfully",
    };
  };

  const runAllTests = async () => {
    setTestResults([]);
    toast("Running centralized data management tests...", { icon: "🧪" });

    await runTest("Data Context", testDataContext);
    await runTest("Sync API", testSyncAPI);
    await runTest("Data Refresh", testDataRefresh);

    toast.success("All tests completed!");
  };

  const clearResults = () => {
    setTestResults([]);
    setSyncStatus(null);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "success":
        return <FiCheck className="w-4 h-4 text-green-600" />;
      case "error":
        return <FiX className="w-4 h-4 text-red-600" />;
      default:
        return <FiInfo className="w-4 h-4 text-blue-600" />;
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Centralized Data Management Test
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={runAllTests}
            disabled={refreshing}
            className="btn-primary text-sm px-3 py-1 flex items-center"
          >
            <FiRefreshCw className={`w-4 h-4 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
            Run Tests
          </button>
          <button
            onClick={clearResults}
            className="btn-secondary text-sm px-3 py-1"
          >
            Clear
          </button>
        </div>
      </div>

      {/* Current Status */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-xs text-gray-600">Plugins Loaded</div>
          <div className="text-lg font-semibold text-gray-900">{plugins.length}</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-xs text-gray-600">Download Data</div>
          <div className="text-lg font-semibold text-gray-900">
            {Object.keys(downloadData).length}
          </div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-xs text-gray-600">Cache Status</div>
          <div className={`text-sm font-medium ${isCacheValid ? 'text-green-600' : 'text-orange-600'}`}>
            {isCacheValid ? 'Valid' : 'Stale'}
          </div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-xs text-gray-600">Last Updated</div>
          <div className="text-sm text-gray-900">
            {lastUpdated ? lastUpdated.toLocaleTimeString() : 'Never'}
          </div>
        </div>
      </div>

      {/* Sync Status */}
      {syncStatus && (
        <div className="bg-blue-50 rounded-lg p-4 mb-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Sync Service Status</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-blue-700">Is Running:</span>
              <span className="ml-2 font-medium">
                {syncStatus.status?.isRunning ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="text-blue-700">Last Sync:</span>
              <span className="ml-2 font-medium">
                {syncStatus.status?.lastSync 
                  ? new Date(syncStatus.status.lastSync).toLocaleString()
                  : 'Never'
                }
              </span>
            </div>
            <div>
              <span className="text-blue-700">Total Plugins:</span>
              <span className="ml-2 font-medium">{syncStatus.summary?.totalPlugins || 0}</span>
            </div>
            <div>
              <span className="text-blue-700">History Records:</span>
              <span className="ml-2 font-medium">{syncStatus.summary?.totalHistoryRecords || 0}</span>
            </div>
          </div>
        </div>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">Test Results</h4>
          {testResults.map((result, index) => (
            <div
              key={index}
              className={`border rounded-lg p-3 ${
                result.status === 'success' 
                  ? 'border-green-200 bg-green-50' 
                  : 'border-red-200 bg-red-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(result.status)}
                  <span className="font-medium text-sm">{result.name}</span>
                </div>
                <span className="text-xs text-gray-500">
                  {result.timestamp.toLocaleTimeString()}
                </span>
              </div>
              <p className="text-sm text-gray-700 mt-1">{result.message}</p>
              {result.data && (
                <details className="mt-2">
                  <summary className="text-xs text-gray-600 cursor-pointer">
                    View Details
                  </summary>
                  <pre className="text-xs bg-white rounded p-2 mt-1 overflow-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DataSyncTest;
