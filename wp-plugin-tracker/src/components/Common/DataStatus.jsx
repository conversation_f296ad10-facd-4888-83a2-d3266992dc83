import React from "react";
import { FiRefreshCw, FiClock, FiDatabase, FiCheck, FiAlertCircle } from "react-icons/fi";
import { useData } from "../../contexts/DataContext";

const DataStatus = ({ showDetails = false, className = "" }) => {
  const {
    loading,
    refreshing,
    lastUpdated,
    isCacheValid,
    cacheAge,
    plugins,
    downloadData,
    refreshAllData,
  } = useData();

  const formatCacheAge = (ageMs) => {
    if (!ageMs) return "Never";
    
    const minutes = Math.floor(ageMs / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ago`;
    if (hours > 0) return `${hours}h ${minutes % 60}m ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return "Just now";
  };

  const getStatusColor = () => {
    if (loading || refreshing) return "text-blue-600";
    if (!isCacheValid) return "text-orange-600";
    return "text-green-600";
  };

  const getStatusIcon = () => {
    if (loading || refreshing) return FiRefreshCw;
    if (!isCacheValid) return FiAlertCircle;
    return FiCheck;
  };

  const StatusIcon = getStatusIcon();

  if (!showDetails) {
    // Compact status indicator
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <StatusIcon 
          className={`w-4 h-4 ${getStatusColor()} ${refreshing ? 'animate-spin' : ''}`} 
        />
        <span className={`text-sm ${getStatusColor()}`}>
          {loading ? "Loading..." : 
           refreshing ? "Refreshing..." :
           isCacheValid ? "Data current" : "Data stale"}
        </span>
      </div>
    );
  }

  // Detailed status panel
  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900 flex items-center">
          <FiDatabase className="w-4 h-4 mr-2" />
          Data Status
        </h3>
        <button
          onClick={refreshAllData}
          disabled={refreshing}
          className="btn-secondary text-xs px-2 py-1 flex items-center"
        >
          <FiRefreshCw className={`w-3 h-3 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      <div className="space-y-3">
        {/* Cache Status */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">Cache Status:</span>
          <div className="flex items-center space-x-1">
            <StatusIcon className={`w-3 h-3 ${getStatusColor()}`} />
            <span className={`text-xs font-medium ${getStatusColor()}`}>
              {isCacheValid ? "Valid" : "Stale"}
            </span>
          </div>
        </div>

        {/* Last Updated */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">Last Updated:</span>
          <div className="flex items-center space-x-1">
            <FiClock className="w-3 h-3 text-gray-400" />
            <span className="text-xs text-gray-900">
              {lastUpdated ? lastUpdated.toLocaleTimeString() : "Never"}
            </span>
          </div>
        </div>

        {/* Cache Age */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">Cache Age:</span>
          <span className="text-xs text-gray-900">
            {formatCacheAge(cacheAge)}
          </span>
        </div>

        {/* Data Counts */}
        <div className="pt-2 border-t border-gray-100">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-600">Plugins:</span>
              <span className="font-medium text-gray-900">{plugins.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Download Data:</span>
              <span className="font-medium text-gray-900">
                {Object.keys(downloadData).length}
              </span>
            </div>
          </div>
        </div>

        {/* Status Message */}
        {!isCacheValid && (
          <div className="pt-2 border-t border-gray-100">
            <p className="text-xs text-orange-600">
              Data is stale. Consider refreshing for latest information.
            </p>
          </div>
        )}

        {refreshing && (
          <div className="pt-2 border-t border-gray-100">
            <p className="text-xs text-blue-600 flex items-center">
              <FiRefreshCw className="w-3 h-3 mr-1 animate-spin" />
              Refreshing data from WordPress.org...
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataStatus;
